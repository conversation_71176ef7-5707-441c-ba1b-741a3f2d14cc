Setup Two Simulators:
Simulator 1: Posting owner 
Simulator 2: Offer owner 

Create Test Data:
On Simulator 1: Create a new posting with a distinctive title -done






 


 LOG  Posting added with ID: a4FU9vRSBlMvodyQhJOO
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: CreatePosting
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: CreatePosting
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.80676841518521, "latitudeDelta": 0.027532028194428904, "longitude": -122.42225942928422, "longitudeDelta": 0.0172518523109062}
 LOG  Debounced region changed: {"latitude": 37.80676841518521, "latitudeDelta": 0.027532028194428904, "longitude": -122.42225942928422, "longitudeDelta": 0.0172518523109062}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.80676841518521, "latitudeDelta": 0.027532028194428904, "longitude": -122.42225942928422, "longitudeDelta": 0.0172518523109062}
 LOG  [fetchPostingsBySearch] Query results: 6
 LOG  [useControlledPostings] Fetched postings: 6
 LOG  [useAuthUser] Cleaning up auth state listener




On Simulator 2: Submit an offer on that posting -done (there's an issue at this step that when the offer owner succesfully submits the offer and navigated to posting detail screen, the newly submitted offer is not displayed under active offers tab. as the offer is not displayed, the offer owner can see the make offer button which is not correct. if a user submits an offer, they should not be allowed to make another offer while they have an active offer)

then i navigated away from posting detail screen to home screen and navigated back to posting detail screen, the offer was displayed under active offers tab correctly







 LOG  [PostingDetail][Navigation] Attempting safe navigation
 LOG  [PostingDetail][Navigation] Checking listeners: {"activeCount": 2, "byCollection": {"offers": 1, "postings": 1}, "timestamp": "2025-06-04T12:51:46.770Z"}
 LOG  [PostingDetail][Navigation] Cleaning up hooks
 LOG  [PostingDetail] Unsubscribing from posting
 LOG  [usePostingDetails][Unsubscribe] Cleaning up listener: {"listenerId": "postings_1749041495665_1749041495665", "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:51:46.773Z"}
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_a4FU9vRSBlMvodyQhJOO", "listenerId": "postings_1749041495665_1749041495665", "timestamp": "2025-06-04T12:51:46.774Z"}
 LOG  [PostingDetail] Successfully unsubscribed from posting
 LOG  [PostingDetail] Unsubscribing from offers
 LOG  [useOffers][Unsubscribe] Cleaning up subscription: {"postingId": "a4FU9vRSBlMvodyQhJOO", "subscriptionId": "offers_1749041495668_1749041495668", "timestamp": "2025-06-04T12:51:46.777Z"}
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_a4FU9vRSBlMvodyQhJOO", "listenerId": "offers_1749041495668_1749041495668", "timestamp": "2025-06-04T12:51:46.778Z"}
 LOG  [PostingDetail] Successfully unsubscribed from offers
 LOG  [ListenerRegistry] Cleanup metrics: {"groupId": null, "metrics": {"duration": 5, "startTime": 1749041506776, "success": true}, "subscriptionId": "postings_1749041495665_1749041495665", "timestamp": "2025-06-04T12:51:46.781Z"}
 LOG  [ListenerRegistry] Cleanup metrics: {"groupId": null, "metrics": {"duration": 6, "startTime": 1749041506778, "success": true}, "subscriptionId": "offers_1749041495668_1749041495668", "timestamp": "2025-06-04T12:51:46.784Z"}
 LOG  [PostingDetail][Navigation] Post-cleanup verification: {"activeListeners": 0, "byCollection": {"offers": 0, "postings": 0}, "timestamp": "2025-06-04T12:51:46.882Z"}
 LOG  [PostingDetail][Navigation] All listeners cleaned up successfully
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: MakeOffer
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: MakeOffer
 LOG  [Navigation] Can go back status: true
 LOG  [BackButton] Rendering back button
 LOG  [MakeOffer][Verify] Checking for active listeners: {"stats": {"activeCleanups": 0, "activeScreens": Set {}, "byCollection": {"offers": 0, "postings": 0}, "lastCleanup": null, "timestamp": "2025-06-04T12:51:46.956Z", "totalListeners": 0}, "timestamp": "2025-06-04T12:51:46.956Z"}
 LOG  [MakeOffer] Checking user restrictions for: ybEyGDxbvleCEXdTvrA2CgVTJ1y2
 LOG  [Navigation] Rendering headerLeft for: MakeOffer
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: MakeOffer
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.81304212227755, "latitudeDelta": 0.019765778432912384, "longitude": -122.41929417923838, "longitudeDelta": 0.01609314918366067}
 LOG  Debounced region changed: {"latitude": 37.81304212227755, "latitudeDelta": 0.019765778432912384, "longitude": -122.41929417923838, "longitudeDelta": 0.01609314918366067}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.81304212227755, "latitudeDelta": 0.019765778432912384, "longitude": -122.41929417923838, "longitudeDelta": 0.01609314918366067}
 LOG  [fetchPostingsBySearch] Query results: 2
 LOG  [useControlledPostings] Fetched postings: 2
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: MakeOffer
 LOG  [Navigation] Can go back status: true
 LOG  [MakeOffer][Verify] Checking for active listeners: {"stats": {"activeCleanups": 0, "activeScreens": Set {}, "byCollection": {"offers": 0, "postings": 0}, "lastCleanup": null, "timestamp": "2025-06-04T12:51:51.957Z", "totalListeners": 0}, "timestamp": "2025-06-04T12:51:51.957Z"}
 LOG  [MakeOffer][Verify] Checking for active listeners: {"stats": {"activeCleanups": 0, "activeScreens": Set {}, "byCollection": {"offers": 0, "postings": 0}, "lastCleanup": null, "timestamp": "2025-06-04T12:51:56.958Z", "totalListeners": 0}, "timestamp": "2025-06-04T12:51:56.959Z"}
 LOG  [MakeOffer] Submitting offer: {"description": "hots offer", "postingId": "a4FU9vRSBlMvodyQhJOO", "price": "111"}
 LOG  [MakeOffer] Creating offer with data: {"postingId": "a4FU9vRSBlMvodyQhJOO", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "price": 111}
 LOG  [addOfferAndDiscussion] Starting with data: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "a4FU9vRSBlMvodyQhJOO", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "price": 111, "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  [addOfferAndDiscussion] Offer created: {"offerId": "zljgB0Joazmfv4mZju6L"}
 LOG  [addOfferAndDiscussion] Discussion created: {"discussionId": "YhRyEyV3t7XlP2Cuw0YM"}
 LOG  [addOfferAndDiscussion] Creating notification: {"body": "You received a new offer of $111", "createdAt": {"_methodName": "serverTimestamp"}, "offerId": "zljgB0Joazmfv4mZju6L", "postingId": "a4FU9vRSBlMvodyQhJOO", "read": false, "recipientId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "senderId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "title": "New Offer", "type": "NEW_OFFER"}
 LOG  [addOfferAndDiscussion] Notification created successfully
 LOG  [addOfferAndDiscussion] Updated user favorites
 LOG  [MakeOffer] Offer created successfully: {"offerId": "zljgB0Joazmfv4mZju6L"}
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: MakeOffer
 LOG  [Navigation] Can go back status: true
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:51:35.639Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:51:58.843Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:51:58.844Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:51:58.845Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_a4FU9vRSBlMvodyQhJOO_1749041495899", "offers_a4FU9vRSBlMvodyQhJOO_1749041495899"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041495899"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041495899"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:51:58.861Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 23225, "timestamp": "2025-06-04T12:51:58.863Z"}
 LOG  [useFavorites][Focus] Screen focused, fetching favorite status
 LOG  [PostingDetail][Focus] Screen focused - debouncing initialization
 LOG  [PostingDetail][Focus] Cleared previous focus timeout
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:51:35.639Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:51:58.864Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:51:58.865Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:51:58.865Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:51:58.872Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 23234, "timestamp": "2025-06-04T12:51:58.872Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 23244, "timestamp": "2025-06-04T12:51:58.882Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041518882", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041518882", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041518882", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041518882", "totalTime": 23250}
 LOG  [PostingDetail][Init] Cleaning up existing subscriptions before setup
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041518882"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041518882"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 23251, "timestamp": "2025-06-04T12:51:58.889Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041518889", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041518889", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041518889", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041518889", "totalTime": 23251}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:51:35.639Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:51:59.004Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:51:59.004Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:51:59.004Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_a4FU9vRSBlMvodyQhJOO_1749041518889", "offers_a4FU9vRSBlMvodyQhJOO_1749041518889"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041518889"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041518889"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:51:59.012Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 23374, "timestamp": "2025-06-04T12:51:59.012Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:51:35.639Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:51:59.015Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:51:59.029Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:51:59.029Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:51:59.035Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 23397, "timestamp": "2025-06-04T12:51:59.035Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 23399, "timestamp": "2025-06-04T12:51:59.037Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041519037", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041519037", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041519037", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041519037", "totalTime": 23399}
 LOG  [PostingDetail][Init] Cleaning up existing subscriptions before setup
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041519037"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041519037"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 23411, "timestamp": "2025-06-04T12:51:59.049Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041519049", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041519049", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041519049", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041519049", "totalTime": 23411}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.81304212209468, "latitudeDelta": 0.021935606611222624, "longitude": -122.41929417923838, "longitudeDelta": 0.01609314918366067}
 LOG  Debounced region changed: {"latitude": 37.81304212209468, "latitudeDelta": 0.021935606611222624, "longitude": -122.41929417923838, "longitudeDelta": 0.01609314918366067}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.81304212209468, "latitudeDelta": 0.021935606611222624, "longitude": -122.41929417923838, "longitudeDelta": 0.01609314918366067}
 LOG  [fetchPostingsBySearch] Query results: 3
 LOG  [useControlledPostings] Fetched postings: 3



navigate away from posting detail to home screen and navigate back to posting detail screen, the offer was displayed under active offers tab correctly 




 LOG  [BackButton] Back button pressed
 LOG  [PostingDetail][Navigation] Attempting safe navigation
 LOG  [PostingDetail][Navigation] Checking listeners: {"activeCount": 0, "byCollection": {"offers": 0, "postings": 0}, "timestamp": "2025-06-04T12:52:29.302Z"}
 LOG  [PostingDetail][Navigation] No active listeners, proceeding with navigation
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [usePostingDetails][Init] Cleaning up on unmount/change
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_a4FU9vRSBlMvodyQhJOO", "listenerId": "postings_1749041495665_1749041495665", "timestamp": "2025-06-04T12:52:29.891Z"}
 LOG  [ListenerRegistry] No entry found for postings_1749041495665_1749041495665
 LOG  [useOffers][Init] Cleaning up on unmount/change
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_a4FU9vRSBlMvodyQhJOO", "listenerId": "offers_1749041495668_1749041495668", "timestamp": "2025-06-04T12:52:29.892Z"}
 LOG  [ListenerRegistry] No entry found for offers_1749041495668_1749041495668
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_a4FU9vRSBlMvodyQhJOO_1749041519049", "offers_a4FU9vRSBlMvodyQhJOO_1749041519049"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041519049"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041519049"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:52:29.896Z"}
 LOG  [PostingDetail][Cleanup] Starting final cleanup
 LOG  [HomeScreen] Marker pressed: {"action": "marker-press", "coordinate": {"latitude": 37.80941363333536, "longitude": -122.42122254306655}, "id": "a4FU9vRSBlMvodyQhJOO", "target": 123}
 LOG  [HomeScreen] Callout pressed for posting: a4FU9vRSBlMvodyQhJOO
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:52:33.211Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.211Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": false, "loading": true, "timestamp": "2025-06-04T12:52:33.212Z"}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [BackButton] Rendering back button
 LOG  [usePostingDetails][Init] Setting up immediate subscription
 LOG  [usePostingDetails][Setup] Creating references
 LOG  [usePostingDetails][Setup] Pre-snapshot listener setup: {"postingId": "a4FU9vRSBlMvodyQhJOO", "ref": "postings/a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.235Z"}
 LOG  [ListenerStateManager] Starting transition for postings_1749041553235_1749041553235: inactive -> initializing
 LOG  [ListenerRegistry][postings] Registered: {"collection": "postings", "id": "1749041553235", "registryId": "postings_1749041553235_1749041553235", "state": "active", "timestamp": "2025-06-04T12:52:33.235Z"}
 LOG  [ListenerStateManager] Completing transition for postings_1749041553235_1749041553235
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_a4FU9vRSBlMvodyQhJOO", "listenerId": "postings_1749041553235_1749041553235", "timestamp": "2025-06-04T12:52:33.236Z"}
 LOG  [ListenerRegistry][postings] Created: {"componentId": "PostingDetail_a4FU9vRSBlMvodyQhJOO", "listenerId": "postings_1749041553235", "timestamp": "2025-06-04T12:52:33.236Z"}
 LOG  [useOffers][Init] Setting up immediate subscription
 LOG  [useOffers][Subscribe] Setting up subscription {"postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.236Z"}
 LOG  [ListenerStateManager] Starting transition for offers_1749041553236_1749041553236: inactive -> initializing
 LOG  [ListenerRegistry][offers] Registered: {"collection": "offers", "id": "1749041553236", "registryId": "offers_1749041553236_1749041553236", "state": "active", "timestamp": "2025-06-04T12:52:33.236Z"}
 LOG  [ListenerStateManager] Completing transition for offers_1749041553236_1749041553236
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_a4FU9vRSBlMvodyQhJOO", "listenerId": "offers_1749041553236_1749041553236", "timestamp": "2025-06-04T12:52:33.237Z"}
 LOG  [ListenerRegistry][offers] Created: {"componentId": "PostingDetail_a4FU9vRSBlMvodyQhJOO", "listenerId": "offers_1749041553236", "timestamp": "2025-06-04T12:52:33.237Z"}
 LOG  [useFavorites][Focus] Screen focused, fetching favorite status
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 26, "timestamp": "2025-06-04T12:52:33.237Z"}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hasDetails": false, "offersCount": 0, "postingLoading": true, "timestamp": "2025-06-04T12:52:33.237Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "a4FU9vRSBlMvodyQhJOO", "loaded": false}, "state": {"activeTab": "active", "currentUserId": null, "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749041553211, "offersCount": 0, "postingDetailsId": "", "userOffer": null}, "timestamp": "2025-06-04T12:52:33.238Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "performance": {"timeSinceLastUpdate": 28}, "props": {"postingId": "a4FU9vRSBlMvodyQhJOO", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": null, "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749041553211, "offersCount": 0, "postingDetailsId": "", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T12:52:33.211Z", "subscriptionCount": 0, "totalTime": 28}, "types": []}, "timestamp": "2025-06-04T12:52:33.239Z"}
 LOG  [PostingDetail][Focus] Screen focused - debouncing initialization
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:52:33.211Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.241Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": true, "postingDetails": null}, "metrics": {"timeSinceStart": 30, "timestamp": "2025-06-04T12:52:33.241Z", "updateCount": 1}, "type": "FETCH_START"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": false, "loading": true, "timestamp": "2025-06-04T12:52:33.241Z"}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:52:33.242Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 32, "timestamp": "2025-06-04T12:52:33.243Z"}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 44, "timestamp": "2025-06-04T12:52:33.255Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041553255", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041553255", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041553255", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041553255", "totalTime": 44}
 LOG  [PostingDetail][Init] Cleaning up existing subscriptions before setup
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041553255"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041553255"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 45, "timestamp": "2025-06-04T12:52:33.256Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041553256", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041553256", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041553256", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041553256", "totalTime": 45}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:52:33.211Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.337Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": true, "postingDetails": null}, "metrics": {"timeSinceStart": 126, "timestamp": "2025-06-04T12:52:33.337Z", "updateCount": 2}, "type": "UPDATE_POSTING"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:52:33.338Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": null, "isFavorite": false, "isOwner": false, "postingId": "", "timestamp": "2025-06-04T12:52:33.338Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_a4FU9vRSBlMvodyQhJOO_1749041553256", "offers_a4FU9vRSBlMvodyQhJOO_1749041553256"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041553256"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041553256"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:52:33.351Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 140, "timestamp": "2025-06-04T12:52:33.351Z"}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hasDetails": true, "offersCount": 0, "postingLoading": false, "timestamp": "2025-06-04T12:52:33.351Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isOwnerCheck": false, "offersCount": 0, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.351Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 0, "timestamp": "2025-06-04T12:52:33.352Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": false, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.352Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "a4FU9vRSBlMvodyQhJOO", "loaded": true}, "state": {"activeTab": "active", "currentUserId": null, "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749041553211, "offersCount": 0, "postingDetailsId": "", "userOffer": null}, "timestamp": "2025-06-04T12:52:33.352Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": true, "isOwner": false, "timestamp": "2025-06-04T12:52:33.352Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:52:33.211Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.353Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:52:33.354Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.354Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:52:33.361Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 150, "timestamp": "2025-06-04T12:52:33.361Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "a4FU9vRSBlMvodyQhJOO", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749041553353, "offersCount": 0, "postingDetailsId": "a4FU9vRSBlMvodyQhJOO", "userOffer": null}, "timestamp": "2025-06-04T12:52:33.361Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "performance": {"timeSinceLastUpdate": 8}, "props": {"postingId": "a4FU9vRSBlMvodyQhJOO", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749041553353, "offersCount": 0, "postingDetailsId": "a4FU9vRSBlMvodyQhJOO", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T12:52:33.211Z", "subscriptionCount": 0, "totalTime": 150}, "types": []}, "timestamp": "2025-06-04T12:52:33.361Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 152, "timestamp": "2025-06-04T12:52:33.363Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041553363", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041553363", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041553363", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041553363", "totalTime": 152}
 LOG  [PostingDetail][Init] Cleaning up existing subscriptions before setup
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041553363"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041553363"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 170, "timestamp": "2025-06-04T12:52:33.381Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041553382", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041553382", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041553382", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041553382", "totalTime": 171}
 LOG  [useOffers][Update] {"changes": [{"id": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:52:33.411Z", "type": "added"}], "count": 1, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.411Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:52:33.211Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.412Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:52:33.412Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.413Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_a4FU9vRSBlMvodyQhJOO_1749041553382", "offers_a4FU9vRSBlMvodyQhJOO_1749041553382"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041553382"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041553382"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:52:33.422Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 211, "timestamp": "2025-06-04T12:52:33.422Z"}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T12:52:33.422Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isOwnerCheck": false, "offersCount": 1, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.423Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T12:52:33.423Z", "userOffers": 1}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": true, "isOwner": false, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.423Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:52:33.211Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.424Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:52:33.425Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.425Z", "userOffer": {"id": "zljgB0Joazmfv4mZju6L", "status": "pending"}}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:52:33.433Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 222, "timestamp": "2025-06-04T12:52:33.433Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "a4FU9vRSBlMvodyQhJOO", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749041553424, "offersCount": 1, "postingDetailsId": "a4FU9vRSBlMvodyQhJOO", "userOffer": {"id": "zljgB0Joazmfv4mZju6L", "status": "pending"}}, "timestamp": "2025-06-04T12:52:33.433Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "performance": {"timeSinceLastUpdate": 9}, "props": {"postingId": "a4FU9vRSBlMvodyQhJOO", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749041553424, "offersCount": 1, "postingDetailsId": "a4FU9vRSBlMvodyQhJOO", "userOffer": {"id": "zljgB0Joazmfv4mZju6L", "status": "pending"}}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T12:52:33.211Z", "subscriptionCount": 0, "totalTime": 222}, "types": []}, "timestamp": "2025-06-04T12:52:33.433Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:52:33.211Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.437Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:52:33.439Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.439Z", "userOffer": {"id": "zljgB0Joazmfv4mZju6L", "status": "pending"}}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:52:33.445Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 234, "timestamp": "2025-06-04T12:52:33.445Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:52:33.211Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.446Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:52:33.446Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:52:33.447Z", "userOffer": {"id": "zljgB0Joazmfv4mZju6L", "status": "pending"}}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:52:33.478Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 267, "timestamp": "2025-06-04T12:52:33.478Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 269, "timestamp": "2025-06-04T12:52:33.480Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041553480", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041553480", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041553480", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041553480", "totalTime": 270}
 LOG  [PostingDetail][Init] Cleaning up existing subscriptions before setup
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041553480"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041553480"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 270, "timestamp": "2025-06-04T12:52:33.481Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041553481", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041553481", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041553481", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041553481", "totalTime": 271}
 LOG  [PostingDetail][Init] Cleaning up existing subscriptions before setup
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041553481"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041553481"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 271, "timestamp": "2025-06-04T12:52:33.482Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041553482", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041553482", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041553482", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041553482", "totalTime": 272}
 LOG  [PostingDetail][Init] Cleaning up existing subscriptions before setup
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041553482"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041553482"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 287, "timestamp": "2025-06-04T12:52:33.498Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041553498", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041553498", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041553498", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041553498", "totalTime": 288}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true




Execute Test:
On Simulator 1: Delete the posting and observe logs









 LOG  === Starting Posting Deletion Flow ===
 LOG  PostingId: a4FU9vRSBlMvodyQhJOO
 LOG  [deletePosting] Starting deletion process {"postingId": "a4FU9vRSBlMvodyQhJOO", "stack": "Error
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:193399:25)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5917:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5931:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5936:14)
    at tryCallTwo (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:61:9)
    at doResolve (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:216:25)
    at Promise (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:82:14)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5928:25)
    at apply (native)
    at deletePosting (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:193636:26)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:261386:56)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5917:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5931:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5936:14)
    at tryCallTwo (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:61:9)
    at doResolve (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:216:25)
    at Promise (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:82:14)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5928:25)
    at apply (native)
    at onPress (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:261401:34)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:42025:21)
    at apply (native)
    at __invokeCallback (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3648:23)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3343:34)
    at __guard (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3551:15)
    at invokeCallbackAndReturnFlushedQueue (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3342:21)", "timestamp": "2025-06-04T12:54:15.012Z"}
 LOG  [deletePosting] Fetching active offers...
 LOG  [deletePosting] Found offers: {"count": 1, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.129Z"}
 LOG  [deletePosting] Marking posting as deleted: {"postingId": "a4FU9vRSBlMvodyQhJOO", "postingTitle": "kous posting to be deleted", "timestamp": "2025-06-04T12:54:15.132Z"}
 LOG  [deletePosting] Processing offer: {"offerId": "zljgB0Joazmfv4mZju6L", "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.134Z"}
 LOG  [deletePosting] Added system message to discussion: {"discussionId": "YhRyEyV3t7XlP2Cuw0YM", "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:15.298Z"}
 LOG  [deletePosting] Creating notification for offer owner: {"offerId": "zljgB0Joazmfv4mZju6L", "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "timestamp": "2025-06-04T12:54:15.299Z", "type": "SYSTEM_WITHDRAWAL"}
 LOG  [deletePosting] Committing batch updates...
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:54:02.128Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.311Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": false, "postingDetails": {"createdAt": [Timestamp], "description": "test", "id": "a4FU9vRSBlMvodyQhJOO", "latitude": 37.80941363333536, "longitude": -122.42122254306655, "postingStatus": "Active", "searchTerms": [Array], "title": "kous posting to be deleted", "titleLower": "kous posting to be deleted", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 13184, "timestamp": "2025-06-04T12:54:15.312Z", "updateCount": 3}, "type": "UPDATE_POSTING"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:54:15.314Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.314Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_a4FU9vRSBlMvodyQhJOO_1749041642363", "offers_a4FU9vRSBlMvodyQhJOO_1749041642363"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041642363"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041642363"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:54:15.328Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 13200, "timestamp": "2025-06-04T12:54:15.328Z"}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T12:54:15.328Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.328Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T12:54:15.329Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.329Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "a4FU9vRSBlMvodyQhJOO", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749041642327, "offersCount": 1, "postingDetailsId": "a4FU9vRSBlMvodyQhJOO", "userOffer": null}, "timestamp": "2025-06-04T12:54:15.329Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": true, "isOwner": true, "timestamp": "2025-06-04T12:54:15.330Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:54:02.128Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.331Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:54:15.332Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.332Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:54:15.340Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 13213, "timestamp": "2025-06-04T12:54:15.341Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "a4FU9vRSBlMvodyQhJOO", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749041655331, "offersCount": 1, "postingDetailsId": "a4FU9vRSBlMvodyQhJOO", "userOffer": null}, "timestamp": "2025-06-04T12:54:15.341Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 10}, "props": {"postingId": "a4FU9vRSBlMvodyQhJOO", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749041655331, "offersCount": 1, "postingDetailsId": "a4FU9vRSBlMvodyQhJOO", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T12:54:02.128Z", "subscriptionCount": 0, "totalTime": 13213}, "types": []}, "timestamp": "2025-06-04T12:54:15.341Z"}
 LOG  [useOffers][Update] {"changes": [{"id": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:15.344Z", "type": "modified"}], "count": 1, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.344Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:54:02.128Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.345Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:54:15.345Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.346Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:54:15.353Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 13225, "timestamp": "2025-06-04T12:54:15.353Z"}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T12:54:15.353Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.353Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T12:54:15.354Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.354Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:54:02.128Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.355Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:54:15.355Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.355Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:54:15.361Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 13233, "timestamp": "2025-06-04T12:54:15.361Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "a4FU9vRSBlMvodyQhJOO", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749041655355, "offersCount": 1, "postingDetailsId": "a4FU9vRSBlMvodyQhJOO", "userOffer": null}, "timestamp": "2025-06-04T12:54:15.361Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 6}, "props": {"postingId": "a4FU9vRSBlMvodyQhJOO", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749041655355, "offersCount": 1, "postingDetailsId": "a4FU9vRSBlMvodyQhJOO", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T12:54:02.128Z", "subscriptionCount": 0, "totalTime": 13233}, "types": []}, "timestamp": "2025-06-04T12:54:15.361Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 13235, "timestamp": "2025-06-04T12:54:15.363Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041655363", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041655363", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041655363", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041655363", "totalTime": 13236}
 LOG  [PostingDetail][Init] Cleaning up existing subscriptions before setup
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041655363"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041655363"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 13236, "timestamp": "2025-06-04T12:54:15.364Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041655364", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041655364", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041655364", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041655364", "totalTime": 13237}
 LOG  [PostingDetail][Init] Cleaning up existing subscriptions before setup
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041655364"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041655364"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 13237, "timestamp": "2025-06-04T12:54:15.365Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041655365", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041655365", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041655365", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041655365", "totalTime": 13238}
 LOG  [PostingDetail][Init] Cleaning up existing subscriptions before setup
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041655365"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041655365"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 13252, "timestamp": "2025-06-04T12:54:15.380Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041655380", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041655380", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041655380", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041655380", "totalTime": 13252}
 LOG  [deletePosting] Notification document created: {"notificationId": "bNMlnHifzYsg55VO11IS", "offerId": "zljgB0Joazmfv4mZju6L", "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "timestamp": "2025-06-04T12:54:15.413Z"}
 LOG  [deletePosting] Sending notifications to offer owners... {"notificationCount": 1, "timestamp": "2025-06-04T12:54:15.485Z"}
 LOG  [deletePosting] Notification results: {"fulfilled": 1, "rejected": 0, "timestamp": "2025-06-04T12:54:15.486Z", "total": 1}
 LOG  [deletePosting] Notification 1 succeeded: {"notificationId": "bNMlnHifzYsg55VO11IS", "timestamp": "2025-06-04T12:54:15.486Z"}
 LOG  [deletePosting] Process completed successfully: {"notificationsSent": 1, "offersProcessed": 1, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.487Z"}
 LOG  Posting deleted successfully
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:54:02.128Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.499Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": false, "postingDetails": {"createdAt": [Timestamp], "deletedAt": null, "description": "test", "id": "a4FU9vRSBlMvodyQhJOO", "latitude": 37.80941363333536, "longitude": -122.42122254306655, "postingStatus": "Deleted", "searchTerms": [Array], "title": "kous posting to be deleted", "titleLower": "kous posting to be deleted", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 13371, "timestamp": "2025-06-04T12:54:15.499Z", "updateCount": 4}, "type": "UPDATE_POSTING"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:54:15.501Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.501Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_a4FU9vRSBlMvodyQhJOO_1749041655380", "offers_a4FU9vRSBlMvodyQhJOO_1749041655380"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041655380"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041655380"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:54:15.509Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 13381, "timestamp": "2025-06-04T12:54:15.509Z"}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T12:54:15.509Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.509Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T12:54:15.509Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.510Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "a4FU9vRSBlMvodyQhJOO", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749041655355, "offersCount": 1, "postingDetailsId": "a4FU9vRSBlMvodyQhJOO", "userOffer": null}, "timestamp": "2025-06-04T12:54:15.510Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": true, "isOwner": true, "timestamp": "2025-06-04T12:54:15.510Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:54:02.128Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.511Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:54:15.512Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.512Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:54:15.517Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 13389, "timestamp": "2025-06-04T12:54:15.517Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "a4FU9vRSBlMvodyQhJOO", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749041655511, "offersCount": 1, "postingDetailsId": "a4FU9vRSBlMvodyQhJOO", "userOffer": null}, "timestamp": "2025-06-04T12:54:15.518Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 7}, "props": {"postingId": "a4FU9vRSBlMvodyQhJOO", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749041655511, "offersCount": 1, "postingDetailsId": "a4FU9vRSBlMvodyQhJOO", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T12:54:02.128Z", "subscriptionCount": 0, "totalTime": 13390}, "types": []}, "timestamp": "2025-06-04T12:54:15.518Z"}
 LOG  [useOffers][Update] {"changes": [{"id": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:15.519Z", "type": "modified"}], "count": 1, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.519Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:54:02.128Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.520Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:54:15.521Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.521Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:54:15.526Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 13399, "timestamp": "2025-06-04T12:54:15.527Z"}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T12:54:15.527Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.527Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T12:54:15.527Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.527Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:54:02.128Z"}, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.528Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:54:15.528Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:15.529Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:54:15.533Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 13405, "timestamp": "2025-06-04T12:54:15.533Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "a4FU9vRSBlMvodyQhJOO", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749041655528, "offersCount": 1, "postingDetailsId": "a4FU9vRSBlMvodyQhJOO", "userOffer": null}, "timestamp": "2025-06-04T12:54:15.533Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 6}, "props": {"postingId": "a4FU9vRSBlMvodyQhJOO", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749041655528, "offersCount": 1, "postingDetailsId": "a4FU9vRSBlMvodyQhJOO", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T12:54:02.128Z", "subscriptionCount": 0, "totalTime": 13406}, "types": []}, "timestamp": "2025-06-04T12:54:15.534Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 13407, "timestamp": "2025-06-04T12:54:15.535Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041655535", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041655535", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041655535", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041655535", "totalTime": 13408}
 LOG  [PostingDetail][Init] Cleaning up existing subscriptions before setup
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041655535"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041655535"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 13408, "timestamp": "2025-06-04T12:54:15.536Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041655536", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041655536", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041655536", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041655536", "totalTime": 13408}
 LOG  [PostingDetail][Init] Cleaning up existing subscriptions before setup
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041655536"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041655536"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 13419, "timestamp": "2025-06-04T12:54:15.547Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041655547", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041655547", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041655547", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041655547", "totalTime": 13420}
 LOG  [PostingDetail][Init] Cleaning up existing subscriptions before setup
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041655547"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041655547"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "a4FU9vRSBlMvodyQhJOO", "timeSinceStart": 13421, "timestamp": "2025-06-04T12:54:15.549Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_a4FU9vRSBlMvodyQhJOO_1749041655549", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_a4FU9vRSBlMvodyQhJOO_1749041655549", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_a4FU9vRSBlMvodyQhJOO_1749041655549", "postingSubId": "posting_a4FU9vRSBlMvodyQhJOO_1749041655549", "totalTime": 13421}



 On Simulator 2: a notification was received regarding to the deletion of the posting and notification bell had a red dot. when i tapped the notification bell icon in home screen, i navigated to the notification screen. i was able to see the new notification and tapped on it, it navigated to the offer detail screen. the offer detail screen was displayed correctly with deletion info.





 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Notifications
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [BackButton] Rendering back button
 LOG  Setting up notifications subscription
 LOG  [notificationService] Setting up notification subscription
 LOG  Notification snapshot received, count: 1
 LOG  Received 1 notifications
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  Notification snapshot received, count: 435
 LOG  Received 435 notifications
 LOG  [NotificationsScreen][handleNotificationPress] Starting with: {"hasData": true, "notificationId": "bNMlnHifzYsg55VO11IS", "offerId": "zljgB0Joazmfv4mZju6L", "stack": "Error
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:272624:29)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5917:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5931:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5936:14)
    at tryCallTwo (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:61:9)
    at doResolve (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:216:25)
    at Promise (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:82:14)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5928:25)
    at apply (native)
    at handleNotificationPress (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:272734:27)
    at onPress (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:272769:41)
    at _performTransitionSideEffects (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79291:22)
    at _receiveSignal (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79241:45)
    at onResponderRelease (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79097:34)
    at apply (native)
    at invokeGuardedCallbackProd (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79798:21)
    at apply (native)
    at invokeGuardedCallback (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79974:42)
    at apply (native)
    at invokeGuardedCallbackAndCatchFirstError (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79988:36)
    at executeDispatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:80065:48)
    at executeDispatchesInOrder (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:80087:26)
    at executeDispatchesAndRelease (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81858:35)
    at executeDispatchesAndReleaseTopLevel (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81865:43)
    at forEach (native)
    at forEachAccumulated (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:80679:22)
    at runEventsInBatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81878:27)
    at runExtractedPluginEventsInBatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81988:25)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81959:42)
    at batchedUpdates$1 (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:95400:20)
    at batchedUpdates (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81834:36)
    at _receiveRootNodeIDEvent (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81958:23)
    at receiveTouches (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:82047:34)
    at apply (native)
    at __callFunction (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3612:36)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3334:31)
    at __guard (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3551:15)
    at callFunctionReturnFlushedQueue (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3333:21)", "type": "SYSTEM_WITHDRAWAL"}
 LOG  Notification snapshot received, count: 435
 LOG  Received 435 notifications
 LOG  [NotificationsScreen] Processing SYSTEM_WITHDRAWAL notification: {"notification": {"createdAt": [Object], "id": "bNMlnHifzYsg55VO11IS", "offerId": "zljgB0Joazmfv4mZju6L", "postingId": "a4FU9vRSBlMvodyQhJOO", "read": false, "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "type": "SYSTEM_WITHDRAWAL"}, "timestamp": "2025-06-04T12:54:48.426Z"}
 LOG  [NotificationsScreen] Navigating to OfferDetail for withdrawn offer: {"offerId": "zljgB0Joazmfv4mZju6L", "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:48.427Z", "type": "SYSTEM_WITHDRAWAL"}
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Notifications
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: OfferDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [BackButton] Rendering back button
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": null, "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-04T12:54:48.481Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": null, "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:54:48.483Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": null, "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": false, "offerOwnerId": null, "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:54:48.488Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": null, "shouldShowAbusiveButton": false, "timestamp": "2025-06-04T12:54:48.500Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.500Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.500Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.501Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.503Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.503Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.503Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.503Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.504Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.504Z"}
 LOG  [useAuthUser] Setting up auth state listener {"currentUser": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "1731681044939", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "1749041366914", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "hasAuth": true, "hasOnAuthStateChanged": true}
 LOG  [OfferDetail][MOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "MOUNT", "mountCount": 0, "params": {"offerId": "zljgB0Joazmfv4mZju6L", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "a4FU9vRSBlMvodyQhJOO", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "timestamp": "2025-06-04T12:54:48.509Z"}
 LOG  [Messages Subscription] Setting up subscription for offerId: zljgB0Joazmfv4mZju6L
 LOG  === OfferDetail Screen Mounted ===
 LOG  Route params: {"offerId": "zljgB0Joazmfv4mZju6L", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "a4FU9vRSBlMvodyQhJOO", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  Setting owner IDs from route params: {"offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [OfferDetail][MOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "MOUNT", "mountCount": 1, "params": {"offerId": "zljgB0Joazmfv4mZju6L", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "a4FU9vRSBlMvodyQhJOO", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "timestamp": "2025-06-04T12:54:48.510Z"}
 LOG  Authenticated user: ybEyGDxbvleCEXdTvrA2CgVTJ1y2
 LOG  [OfferDetail] Initializing with params: {"hasInitialOffer": false, "hasInitialPosting": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.511Z"}
 LOG  [OfferDetail] Setting postingOwnerId from route params: {"postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [OfferDetail] Setting offerOwnerId from route params: {"offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  OfferDetail refresh button states: {"showOfferRefresh": false, "showOfferRefreshExplicit": false, "showPostingRefresh": false, "showPostingRefreshExplicit": false, "timestamp": "2025-06-04T12:54:48.512Z"}
 LOG  [OfferDetail][NOTIFICATION_LISTENER_SETUP] {"offerId": "zljgB0Joazmfv4mZju6L", "postingId": undefined, "timestamp": "2025-06-04T12:54:48.512Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_LISTENER_SETUP] {"offerId": "zljgB0Joazmfv4mZju6L", "postingId": undefined, "timestamp": "2025-06-04T12:54:48.513Z", "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-04T12:54:48.516Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:54:48.516Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:54:48.520Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.528Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.528Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.528Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.528Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.528Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-04T12:54:48.529Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.529Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.529Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.529Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.529Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.529Z"}
 LOG  [useAuthUser] Auth state changed {"user": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "1731681044939", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "1749041366914", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}}
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 0, "timestamp": "2025-06-04T12:54:48.546Z"}
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.81304212227755, "latitudeDelta": 0.019765778432912384, "longitude": -122.41929417923838, "longitudeDelta": 0.01609314918366067}
 LOG  [Messages Subscription] Received snapshot update: {"empty": false, "metadata": {"fromCache": false, "hasPendingWrites": false}, "size": 1}
 LOG  [Messages Subscription] Processing discussion: {"id": "YhRyEyV3t7XlP2Cuw0YM", "messageCount": 1}
 LOG  [Messages Subscription] Raw messages: {"count": 1, "messageKeys": ["timestamp", "type", "senderId", "text", "read"], "sampleMessage": {"read": true, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749041655}, "type": "system"}}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["timestamp", "type", "senderId", "text", "read"], "timestampType": "object"}
 LOG  [Messages Subscription] Validation summary: {"firstValidMessage": {"id": undefined, "senderId": "system", "timestamp": {"nanoseconds": 0, "seconds": 1749041655}}, "invalidMessages": 0, "totalMessages": 1, "validMessages": 1}
 LOG  [Messages Subscription] Processing messages: {"hasLegacyMessages": true, "messageTypes": [{"hasId": false, "hasTimestamp": true, "timestampType": "object"}], "newCount": 1, "prevCount": 0}
 LOG  [Messages Subscription] Updated messages state: {"firstMessage": {"id": undefined, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749041655}}, "hasLegacyMessages": true, "lastMessage": {"id": undefined, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749041655}}, "newCount": 1, "previousCount": 0}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-04T12:54:48.704Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:54:48.705Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:54:48.709Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.723Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.723Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.723Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.723Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.723Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-04T12:54:48.725Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.725Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.725Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.725Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.725Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.725Z"}
 LOG  Marking messages as read for authorized user: {"isOfferOwner": true, "isPostingOwner": false, "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  Initial load, scrolling to end: 1
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-04T12:54:48.729Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:54:48.729Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:54:48.735Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.745Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.745Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.745Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.746Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.746Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-04T12:54:48.746Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.746Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.746Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.746Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.747Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.747Z"}
 LOG  FlatList layout complete
 LOG  Skipping load more: {"hasLastDoc": false, "hasMoreMessages": true, "lastDocId": undefined, "loadingMore": false}
 LOG  [OfferCache][SET] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.772Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "zljgB0Joazmfv4mZju6L", "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:48.772Z"}
 LOG  [OfferCache][SET] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.772Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "zljgB0Joazmfv4mZju6L", "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:48.773Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Deleted", "postingTitle": "kous posting to be deleted", "timestamp": "2025-06-04T12:54:48.774Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:54:48.775Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:54:48.780Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.786Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.786Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.787Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.788Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.788Z"}
 LOG  [OfferDetail][NOTIFICATION_LISTENER_CLEANUP] {"offerId": "zljgB0Joazmfv4mZju6L", "postingId": undefined, "timestamp": "2025-06-04T12:54:48.788Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-04T12:54:48.788Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.789Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.789Z"}
 LOG  [OfferCache][GET] {"age": 16, "found": true, "isExpired": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.789Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.789Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.789Z"}
 LOG  [OfferDetail][NOTIFICATION_LISTENER_SETUP] {"offerId": "zljgB0Joazmfv4mZju6L", "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:48.789Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_LISTENER_SETUP] {"offerId": "zljgB0Joazmfv4mZju6L", "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:48.790Z", "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Deleted", "postingTitle": "kous posting to be deleted", "timestamp": "2025-06-04T12:54:48.791Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:54:48.792Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:54:48.796Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.803Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.803Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.803Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.803Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.819Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-04T12:54:48.820Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.820Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.820Z"}
 LOG  [OfferCache][GET] {"age": 47, "found": true, "isExpired": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.820Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.821Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.821Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Deleted", "postingTitle": "kous posting to be deleted", "timestamp": "2025-06-04T12:54:48.823Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:54:48.823Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:54:48.828Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.836Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.836Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.836Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.836Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.836Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-04T12:54:48.837Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.837Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.837Z"}
 LOG  [OfferCache][GET] {"age": 64, "found": true, "isExpired": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.837Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.837Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.837Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hots offer", "hookPrice": 111, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:54:48.839Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hots offer", "offerId": "zljgB0Joazmfv4mZju6L", "price": 111, "timestamp": "2025-06-04T12:54:48.841Z"}
 LOG  [OfferCache][SET] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.844Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "zljgB0Joazmfv4mZju6L", "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:48.844Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 0, "timestamp": "2025-06-04T12:54:48.851Z"}
 LOG  Skipping load more: {"hasLastDoc": false, "hasMoreMessages": true, "lastDocId": undefined, "loadingMore": false}
 LOG  FlatList layout complete
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Deleted", "postingTitle": "kous posting to be deleted", "timestamp": "2025-06-04T12:54:48.881Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:54:48.881Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:54:48.886Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.893Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.894Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.894Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.895Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.895Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-04T12:54:48.895Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.895Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.895Z"}
 LOG  [OfferCache][GET] {"age": 52, "found": true, "isExpired": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.896Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.896Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:48.896Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hots offer", "hookPrice": 111, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "111", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:54:48.897Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hots offer", "offerId": "zljgB0Joazmfv4mZju6L", "price": 111, "timestamp": "2025-06-04T12:54:48.901Z"}
 LOG  Debounced region changed: {"latitude": 37.81304212227755, "latitudeDelta": 0.019765778432912384, "longitude": -122.41929417923838, "longitudeDelta": 0.01609314918366067}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.81304212227755, "latitudeDelta": 0.019765778432912384, "longitude": -122.41929417923838, "longitudeDelta": 0.01609314918366067}
 LOG  [OfferCache][SET] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:49.017Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "zljgB0Joazmfv4mZju6L", "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:49.017Z"}
 LOG  [OfferCache][SET] {"offerId": "zljgB0Joazmfv4mZju6L", "timestamp": "2025-06-04T12:54:49.019Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "zljgB0Joazmfv4mZju6L", "postingId": "a4FU9vRSBlMvodyQhJOO", "timestamp": "2025-06-04T12:54:49.019Z"}
 LOG  [fetchPostingsBySearch] Query results: 1
 LOG  [useControlledPostings] Fetched postings: 1
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
