Setup Two Simulators:
Simulator 1: Posting owner 
Simulator 2: Offer owner 

Create Test Data:
On Simulator 1: Create a new posting with a distinctive title -done

 LOG  Posting added with ID: QWWEivga748ZQDcbfyVy
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: CreatePosting
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: CreatePosting
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.81254612432822, "latitudeDelta": 0.013764937032583191, "longitude": -122.4191397585568, "longitudeDelta": 0.008625926155460206}
 LOG  Debounced region changed: {"latitude": 37.81254612432822, "latitudeDelta": 0.013764937032583191, "longitude": -122.4191397585568, "longitudeDelta": 0.008625926155460206}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.81254612432822, "latitudeDelta": 0.013764937032583191, "longitude": -122.4191397585568, "longitudeDelta": 0.008625926155460206}
 LOG  [fetchPostingsBySearch] Query results: 2
 LOG  [useControlledPostings] Fetched postings: 2
 LOG  [useAuthUser] Cleaning up auth state listener



On Simulator 2: Submit an offer on that posting -done (the offer was immediately displayed under active offers tab after returning to posting detail screen)





 LOG  [MakeOffer][Verify] Checking for active listeners: {"stats": {"activeCleanups": 0, "activeScreens": Set {}, "byCollection": {"offers": 0, "postings": 0}, "lastCleanup": null, "timestamp": "2025-06-04T16:16:16.841Z", "totalListeners": 0}, "timestamp": "2025-06-04T16:16:16.841Z"}
 LOG  [MakeOffer][Verify] Checking for active listeners: {"stats": {"activeCleanups": 0, "activeScreens": Set {}, "byCollection": {"offers": 0, "postings": 0}, "lastCleanup": null, "timestamp": "2025-06-04T16:16:21.842Z", "totalListeners": 0}, "timestamp": "2025-06-04T16:16:21.842Z"}
 LOG  [MakeOffer] Submitting offer: {"description": "hots offer", "postingId": "QWWEivga748ZQDcbfyVy", "price": "111"}
 LOG  [MakeOffer] Creating offer with data: {"postingId": "QWWEivga748ZQDcbfyVy", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "price": 111}
 LOG  [addOfferAndDiscussion] Starting with data: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "QWWEivga748ZQDcbfyVy", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "price": 111, "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  [addOfferAndDiscussion] Offer created: {"offerId": "Zzo2UV9W0vDn32fEp9of"}
 LOG  [addOfferAndDiscussion] Discussion created: {"discussionId": "WX58ShLpzxC8IamUQWDc"}
 LOG  [addOfferAndDiscussion] Creating notification: {"body": "You received a new offer of $111", "createdAt": {"_methodName": "serverTimestamp"}, "offerId": "Zzo2UV9W0vDn32fEp9of", "postingId": "QWWEivga748ZQDcbfyVy", "read": false, "recipientId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "senderId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "title": "New Offer", "type": "NEW_OFFER"}
 LOG  [addOfferAndDiscussion] Notification created successfully
 LOG  [addOfferAndDiscussion] Updated user favorites
 LOG  [MakeOffer] Offer created successfully: {"offerId": "Zzo2UV9W0vDn32fEp9of"}
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: MakeOffer
 LOG  [Navigation] Can go back status: true
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T16:16:05.475Z"}, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.182Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T16:16:24.185Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.185Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_QWWEivga748ZQDcbfyVy_1749053765707", "offers_QWWEivga748ZQDcbfyVy_1749053765707"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053765707"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053765707"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T16:16:24.200Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 18726, "timestamp": "2025-06-04T16:16:24.200Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 18726, "timestamp": "2025-06-04T16:16:24.200Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053784201", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053784201", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_QWWEivga748ZQDcbfyVy_1749053784201", "postingSubId": "posting_QWWEivga748ZQDcbfyVy_1749053784201", "totalTime": 18728}
 LOG  [usePostingDetails][Focus] Screen focused, setting up subscription
 LOG  [usePostingDetails][Setup] Creating references
 LOG  [usePostingDetails][Setup] Pre-snapshot listener setup: {"postingId": "QWWEivga748ZQDcbfyVy", "ref": "postings/QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.202Z"}
 LOG  [ListenerStateManager] Starting transition for postings_1749053784202_1749053784202: inactive -> initializing
 LOG  [ListenerRegistry][postings] Registered: {"collection": "postings", "id": "1749053784202", "registryId": "postings_1749053784202_1749053784202", "state": "active", "timestamp": "2025-06-04T16:16:24.202Z"}
 LOG  [ListenerStateManager] Completing transition for postings_1749053784202_1749053784202
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_QWWEivga748ZQDcbfyVy", "listenerId": "postings_1749053784202_1749053784202", "timestamp": "2025-06-04T16:16:24.204Z"}
 LOG  [ListenerRegistry][postings] Created: {"componentId": "PostingDetail_QWWEivga748ZQDcbfyVy", "listenerId": "postings_1749053784202", "timestamp": "2025-06-04T16:16:24.204Z"}
 LOG  [useOffers][Focus] Screen focused, setting up subscription
 LOG  [useOffers][Subscribe] Setting up subscription {"postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.204Z"}
 LOG  [ListenerStateManager] Starting transition for offers_1749053784204_1749053784204: inactive -> initializing
 LOG  [ListenerRegistry][offers] Registered: {"collection": "offers", "id": "1749053784204", "registryId": "offers_1749053784204_1749053784204", "state": "active", "timestamp": "2025-06-04T16:16:24.205Z"}
 LOG  [ListenerStateManager] Completing transition for offers_1749053784204_1749053784204
 LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_QWWEivga748ZQDcbfyVy", "listenerId": "offers_1749053784204_1749053784204", "timestamp": "2025-06-04T16:16:24.205Z"}
 LOG  [ListenerRegistry][offers] Created: {"componentId": "PostingDetail_QWWEivga748ZQDcbfyVy", "listenerId": "offers_1749053784204", "timestamp": "2025-06-04T16:16:24.205Z"}
 LOG  [useFavorites][Focus] Screen focused, fetching favorite status
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T16:16:05.475Z"}, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.206Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": false, "postingDetails": {"createdAt": [Timestamp], "description": "test", "id": "QWWEivga748ZQDcbfyVy", "latitude": 37.811550047716196, "longitude": -122.42065633631665, "postingStatus": "Active", "searchTerms": [Array], "title": "kous surgical fix test", "titleLower": "kous surgical fix test", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 18732, "timestamp": "2025-06-04T16:16:24.207Z", "updateCount": 3}, "type": "FETCH_START"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": true, "timestamp": "2025-06-04T16:16:24.209Z"}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_QWWEivga748ZQDcbfyVy_1749053784201", "offers_QWWEivga748ZQDcbfyVy_1749053784201"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053784201"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053784201"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T16:16:24.212Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 18738, "timestamp": "2025-06-04T16:16:24.212Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 18738, "timestamp": "2025-06-04T16:16:24.212Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053784213", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053784213", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_QWWEivga748ZQDcbfyVy_1749053784213", "postingSubId": "posting_QWWEivga748ZQDcbfyVy_1749053784213", "totalTime": 18739}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hasDetails": true, "offersCount": 0, "postingLoading": true, "timestamp": "2025-06-04T16:16:24.213Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T16:16:05.475Z"}, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.347Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": true, "postingDetails": {"createdAt": [Timestamp], "description": "test", "id": "QWWEivga748ZQDcbfyVy", "latitude": 37.811550047716196, "longitude": -122.42065633631665, "postingStatus": "Active", "searchTerms": [Array], "title": "kous surgical fix test", "titleLower": "kous surgical fix test", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 18872, "timestamp": "2025-06-04T16:16:24.347Z", "updateCount": 4}, "type": "UPDATE_POSTING"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T16:16:24.349Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.349Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_QWWEivga748ZQDcbfyVy_1749053784213", "offers_QWWEivga748ZQDcbfyVy_1749053784213"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053784213"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053784213"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T16:16:24.364Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 18890, "timestamp": "2025-06-04T16:16:24.364Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 18890, "timestamp": "2025-06-04T16:16:24.364Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053784364", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053784364", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_QWWEivga748ZQDcbfyVy_1749053784364", "postingSubId": "posting_QWWEivga748ZQDcbfyVy_1749053784364", "totalTime": 18891}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hasDetails": true, "offersCount": 0, "postingLoading": false, "timestamp": "2025-06-04T16:16:24.365Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isOwnerCheck": false, "offersCount": 0, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.365Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 0, "timestamp": "2025-06-04T16:16:24.365Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": false, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.365Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "QWWEivga748ZQDcbfyVy", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749053765686, "offersCount": 0, "postingDetailsId": "QWWEivga748ZQDcbfyVy", "userOffer": null}, "timestamp": "2025-06-04T16:16:24.365Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": true, "isOwner": false, "timestamp": "2025-06-04T16:16:24.366Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T16:16:05.475Z"}, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.367Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T16:16:24.368Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.368Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_QWWEivga748ZQDcbfyVy_1749053784364", "offers_QWWEivga748ZQDcbfyVy_1749053784364"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053784364"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053784364"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T16:16:24.375Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 18901, "timestamp": "2025-06-04T16:16:24.375Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 18901, "timestamp": "2025-06-04T16:16:24.375Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053784375", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053784375", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_QWWEivga748ZQDcbfyVy_1749053784375", "postingSubId": "posting_QWWEivga748ZQDcbfyVy_1749053784375", "totalTime": 18902}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "QWWEivga748ZQDcbfyVy", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749053784367, "offersCount": 0, "postingDetailsId": "QWWEivga748ZQDcbfyVy", "userOffer": null}, "timestamp": "2025-06-04T16:16:24.376Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "performance": {"timeSinceLastUpdate": 9}, "props": {"postingId": "QWWEivga748ZQDcbfyVy", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749053784367, "offersCount": 0, "postingDetailsId": "QWWEivga748ZQDcbfyVy", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T16:16:05.474Z", "subscriptionCount": 0, "totalTime": 18902}, "types": []}, "timestamp": "2025-06-04T16:16:24.376Z"}
 LOG  [useOffers][Update] {"changes": [{"id": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:16:24.381Z", "type": "added"}], "count": 1, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.381Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T16:16:05.475Z"}, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.383Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T16:16:24.383Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.383Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_QWWEivga748ZQDcbfyVy_1749053784375", "offers_QWWEivga748ZQDcbfyVy_1749053784375"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053784375"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053784375"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T16:16:24.392Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 18918, "timestamp": "2025-06-04T16:16:24.392Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 18918, "timestamp": "2025-06-04T16:16:24.392Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053784392", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053784392", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_QWWEivga748ZQDcbfyVy_1749053784392", "postingSubId": "posting_QWWEivga748ZQDcbfyVy_1749053784392", "totalTime": 18918}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T16:16:24.392Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isOwnerCheck": false, "offersCount": 1, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.394Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T16:16:24.394Z", "userOffers": 1}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": true, "isOwner": false, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.394Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T16:16:05.475Z"}, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.395Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T16:16:24.395Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.396Z", "userOffer": {"id": "Zzo2UV9W0vDn32fEp9of", "status": "pending"}}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_QWWEivga748ZQDcbfyVy_1749053784392", "offers_QWWEivga748ZQDcbfyVy_1749053784392"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053784392"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053784392"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T16:16:24.402Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 18929, "timestamp": "2025-06-04T16:16:24.403Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 18929, "timestamp": "2025-06-04T16:16:24.403Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053784403", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053784403", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_QWWEivga748ZQDcbfyVy_1749053784403", "postingSubId": "posting_QWWEivga748ZQDcbfyVy_1749053784403", "totalTime": 18929}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "QWWEivga748ZQDcbfyVy", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749053784395, "offersCount": 1, "postingDetailsId": "QWWEivga748ZQDcbfyVy", "userOffer": {"id": "Zzo2UV9W0vDn32fEp9of", "status": "pending"}}, "timestamp": "2025-06-04T16:16:24.403Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "performance": {"timeSinceLastUpdate": 8}, "props": {"postingId": "QWWEivga748ZQDcbfyVy", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749053784395, "offersCount": 1, "postingDetailsId": "QWWEivga748ZQDcbfyVy", "userOffer": {"id": "Zzo2UV9W0vDn32fEp9of", "status": "pending"}}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T16:16:05.474Z", "subscriptionCount": 0, "totalTime": 18929}, "types": []}, "timestamp": "2025-06-04T16:16:24.403Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T16:16:05.475Z"}, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.407Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T16:16:24.407Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.407Z", "userOffer": {"id": "Zzo2UV9W0vDn32fEp9of", "status": "pending"}}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_QWWEivga748ZQDcbfyVy_1749053784403", "offers_QWWEivga748ZQDcbfyVy_1749053784403"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053784403"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053784403"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T16:16:24.413Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 18939, "timestamp": "2025-06-04T16:16:24.413Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 18940, "timestamp": "2025-06-04T16:16:24.414Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053784414", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053784414", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_QWWEivga748ZQDcbfyVy_1749053784414", "postingSubId": "posting_QWWEivga748ZQDcbfyVy_1749053784414", "totalTime": 18940}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T16:16:05.475Z"}, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.415Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T16:16:24.415Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:16:24.415Z", "userOffer": {"id": "Zzo2UV9W0vDn32fEp9of", "status": "pending"}}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_QWWEivga748ZQDcbfyVy_1749053784414", "offers_QWWEivga748ZQDcbfyVy_1749053784414"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053784414"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053784414"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T16:16:24.421Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 18948, "timestamp": "2025-06-04T16:16:24.422Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 18948, "timestamp": "2025-06-04T16:16:24.422Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053784422", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053784422", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_QWWEivga748ZQDcbfyVy_1749053784422", "postingSubId": "posting_QWWEivga748ZQDcbfyVy_1749053784422", "totalTime": 18949}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.81134209016356, "latitudeDelta": 0.021936111693584337, "longitude": -122.41817964989167, "longitudeDelta": 0.01609314918364646}
 LOG  Debounced region changed: {"latitude": 37.81134209016356, "latitudeDelta": 0.021936111693584337, "longitude": -122.41817964989167, "longitudeDelta": 0.01609314918364646}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.81134209016356, "latitudeDelta": 0.021936111693584337, "longitude": -122.41817964989167, "longitudeDelta": 0.01609314918364646}
 LOG  [fetchPostingsBySearch] Query results: 4
 LOG  [useControlledPostings] Fetched postings: 4




Execute Test:
On Simulator 1: Delete the posting and observe logs





 LOG  === Starting Posting Deletion Flow ===
 LOG  PostingId: QWWEivga748ZQDcbfyVy
 LOG  [deletePosting] Starting deletion process {"postingId": "QWWEivga748ZQDcbfyVy", "stack": "Error
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:193399:25)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5917:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5931:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5936:14)
    at tryCallTwo (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:61:9)
    at doResolve (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:216:25)
    at Promise (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:82:14)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5928:25)
    at apply (native)
    at deletePosting (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:193636:26)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:261386:56)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5917:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5931:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5936:14)
    at tryCallTwo (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:61:9)
    at doResolve (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:216:25)
    at Promise (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:82:14)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5928:25)
    at apply (native)
    at onPress (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:261401:34)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:42025:21)
    at apply (native)
    at __invokeCallback (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3648:23)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3343:34)
    at __guard (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3551:15)
    at invokeCallbackAndReturnFlushedQueue (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3342:21)", "timestamp": "2025-06-04T16:17:44.500Z"}
 LOG  [deletePosting] Fetching active offers...
 LOG  [deletePosting] Found offers: {"count": 1, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.601Z"}
 LOG  [deletePosting] Marking posting as deleted: {"postingId": "QWWEivga748ZQDcbfyVy", "postingTitle": "kous surgical fix test", "timestamp": "2025-06-04T16:17:44.604Z"}
 LOG  [deletePosting] Processing offer: {"offerId": "Zzo2UV9W0vDn32fEp9of", "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.608Z"}
 LOG  [deletePosting] Added system message to discussion: {"discussionId": "WX58ShLpzxC8IamUQWDc", "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:17:44.779Z"}
 LOG  [deletePosting] Creating notification for offer owner: {"offerId": "Zzo2UV9W0vDn32fEp9of", "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "timestamp": "2025-06-04T16:17:44.780Z", "type": "SYSTEM_WITHDRAWAL"}
 LOG  [deletePosting] Committing batch updates...
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T16:17:36.247Z"}, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.791Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": false, "postingDetails": {"createdAt": [Timestamp], "description": "test", "id": "QWWEivga748ZQDcbfyVy", "latitude": 37.811550047716196, "longitude": -122.42065633631665, "postingStatus": "Active", "searchTerms": [Array], "title": "kous surgical fix test", "titleLower": "kous surgical fix test", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 8544, "timestamp": "2025-06-04T16:17:44.791Z", "updateCount": 3}, "type": "UPDATE_POSTING"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T16:17:44.793Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.794Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_QWWEivga748ZQDcbfyVy_1749053856489", "offers_QWWEivga748ZQDcbfyVy_1749053856489"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053856489"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053856489"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T16:17:44.806Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 8560, "timestamp": "2025-06-04T16:17:44.806Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 8560, "timestamp": "2025-06-04T16:17:44.806Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053864806", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053864806", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_QWWEivga748ZQDcbfyVy_1749053864806", "postingSubId": "posting_QWWEivga748ZQDcbfyVy_1749053864806", "totalTime": 8561}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T16:17:44.807Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.807Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T16:17:44.810Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.810Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "QWWEivga748ZQDcbfyVy", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749053856466, "offersCount": 1, "postingDetailsId": "QWWEivga748ZQDcbfyVy", "userOffer": null}, "timestamp": "2025-06-04T16:17:44.810Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": true, "isOwner": true, "timestamp": "2025-06-04T16:17:44.811Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T16:17:36.247Z"}, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.812Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T16:17:44.812Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.813Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_QWWEivga748ZQDcbfyVy_1749053864806", "offers_QWWEivga748ZQDcbfyVy_1749053864806"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053864806"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053864806"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T16:17:44.821Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 8575, "timestamp": "2025-06-04T16:17:44.821Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 8576, "timestamp": "2025-06-04T16:17:44.822Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053864822", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053864822", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_QWWEivga748ZQDcbfyVy_1749053864822", "postingSubId": "posting_QWWEivga748ZQDcbfyVy_1749053864822", "totalTime": 8576}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "QWWEivga748ZQDcbfyVy", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749053864812, "offersCount": 1, "postingDetailsId": "QWWEivga748ZQDcbfyVy", "userOffer": null}, "timestamp": "2025-06-04T16:17:44.822Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 11}, "props": {"postingId": "QWWEivga748ZQDcbfyVy", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749053864812, "offersCount": 1, "postingDetailsId": "QWWEivga748ZQDcbfyVy", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T16:17:36.246Z", "subscriptionCount": 0, "totalTime": 8577}, "types": []}, "timestamp": "2025-06-04T16:17:44.823Z"}
 LOG  [useOffers][Update] {"changes": [{"id": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:17:44.825Z", "type": "modified"}], "count": 1, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.825Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T16:17:36.247Z"}, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.827Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T16:17:44.827Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.827Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_QWWEivga748ZQDcbfyVy_1749053864822", "offers_QWWEivga748ZQDcbfyVy_1749053864822"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053864822"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053864822"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T16:17:44.835Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 8589, "timestamp": "2025-06-04T16:17:44.835Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 8589, "timestamp": "2025-06-04T16:17:44.835Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053864835", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053864835", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_QWWEivga748ZQDcbfyVy_1749053864835", "postingSubId": "posting_QWWEivga748ZQDcbfyVy_1749053864835", "totalTime": 8590}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T16:17:44.836Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.836Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T16:17:44.836Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.836Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T16:17:36.247Z"}, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.837Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T16:17:44.838Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.838Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_QWWEivga748ZQDcbfyVy_1749053864835", "offers_QWWEivga748ZQDcbfyVy_1749053864835"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053864835"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053864835"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T16:17:44.843Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 8598, "timestamp": "2025-06-04T16:17:44.844Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 8598, "timestamp": "2025-06-04T16:17:44.844Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053864844", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053864844", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_QWWEivga748ZQDcbfyVy_1749053864844", "postingSubId": "posting_QWWEivga748ZQDcbfyVy_1749053864844", "totalTime": 8598}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "QWWEivga748ZQDcbfyVy", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749053864837, "offersCount": 1, "postingDetailsId": "QWWEivga748ZQDcbfyVy", "userOffer": null}, "timestamp": "2025-06-04T16:17:44.844Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 8}, "props": {"postingId": "QWWEivga748ZQDcbfyVy", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749053864837, "offersCount": 1, "postingDetailsId": "QWWEivga748ZQDcbfyVy", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T16:17:36.246Z", "subscriptionCount": 0, "totalTime": 8599}, "types": []}, "timestamp": "2025-06-04T16:17:44.845Z"}
 LOG  [deletePosting] Notification document created: {"notificationId": "BmgyWY2aOYmQ1tFLyjcm", "offerId": "Zzo2UV9W0vDn32fEp9of", "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "timestamp": "2025-06-04T16:17:44.904Z"}
 LOG  [deletePosting] Sending notifications to offer owners... {"notificationCount": 1, "timestamp": "2025-06-04T16:17:44.964Z"}
 LOG  [deletePosting] Notification results: {"fulfilled": 1, "rejected": 0, "timestamp": "2025-06-04T16:17:44.965Z", "total": 1}
 LOG  [deletePosting] Notification 1 succeeded: {"notificationId": "BmgyWY2aOYmQ1tFLyjcm", "timestamp": "2025-06-04T16:17:44.966Z"}
 LOG  [deletePosting] Process completed successfully: {"notificationsSent": 1, "offersProcessed": 1, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.966Z"}
 LOG  Posting deleted successfully
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T16:17:36.247Z"}, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.987Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": false, "postingDetails": {"createdAt": [Timestamp], "deletedAt": null, "description": "test", "id": "QWWEivga748ZQDcbfyVy", "latitude": 37.811550047716196, "longitude": -122.42065633631665, "postingStatus": "Deleted", "searchTerms": [Array], "title": "kous surgical fix test", "titleLower": "kous surgical fix test", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 8740, "timestamp": "2025-06-04T16:17:44.987Z", "updateCount": 4}, "type": "UPDATE_POSTING"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T16:17:44.989Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.989Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_QWWEivga748ZQDcbfyVy_1749053864844", "offers_QWWEivga748ZQDcbfyVy_1749053864844"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053864844"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053864844"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T16:17:44.995Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 8749, "timestamp": "2025-06-04T16:17:44.995Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 8749, "timestamp": "2025-06-04T16:17:44.995Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053864995", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053864995", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_QWWEivga748ZQDcbfyVy_1749053864995", "postingSubId": "posting_QWWEivga748ZQDcbfyVy_1749053864995", "totalTime": 8750}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T16:17:44.996Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.996Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T16:17:44.996Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.996Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "QWWEivga748ZQDcbfyVy", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749053864837, "offersCount": 1, "postingDetailsId": "QWWEivga748ZQDcbfyVy", "userOffer": null}, "timestamp": "2025-06-04T16:17:44.996Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": true, "isOwner": true, "timestamp": "2025-06-04T16:17:44.998Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T16:17:36.247Z"}, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:44.999Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T16:17:44.999Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:45.000Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_QWWEivga748ZQDcbfyVy_1749053864995", "offers_QWWEivga748ZQDcbfyVy_1749053864995"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053864995"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053864995"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T16:17:45.005Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 8759, "timestamp": "2025-06-04T16:17:45.005Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 8759, "timestamp": "2025-06-04T16:17:45.005Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053865005", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053865005", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_QWWEivga748ZQDcbfyVy_1749053865005", "postingSubId": "posting_QWWEivga748ZQDcbfyVy_1749053865005", "totalTime": 8759}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "QWWEivga748ZQDcbfyVy", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749053864999, "offersCount": 1, "postingDetailsId": "QWWEivga748ZQDcbfyVy", "userOffer": null}, "timestamp": "2025-06-04T16:17:45.006Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 7}, "props": {"postingId": "QWWEivga748ZQDcbfyVy", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749053864999, "offersCount": 1, "postingDetailsId": "QWWEivga748ZQDcbfyVy", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T16:17:36.246Z", "subscriptionCount": 0, "totalTime": 8760}, "types": []}, "timestamp": "2025-06-04T16:17:45.006Z"}
 LOG  [useOffers][Update] {"changes": [{"id": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:17:45.007Z", "type": "modified"}], "count": 1, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:45.007Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T16:17:36.247Z"}, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:45.008Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T16:17:45.009Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:45.009Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_QWWEivga748ZQDcbfyVy_1749053865005", "offers_QWWEivga748ZQDcbfyVy_1749053865005"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053865005"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053865005"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T16:17:45.015Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 8769, "timestamp": "2025-06-04T16:17:45.015Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 8769, "timestamp": "2025-06-04T16:17:45.015Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053865016", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053865016", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_QWWEivga748ZQDcbfyVy_1749053865016", "postingSubId": "posting_QWWEivga748ZQDcbfyVy_1749053865016", "totalTime": 8770}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T16:17:45.016Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:45.016Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T16:17:45.016Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:45.016Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T16:17:36.247Z"}, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:45.017Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T16:17:45.018Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:17:45.018Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_QWWEivga748ZQDcbfyVy_1749053865016", "offers_QWWEivga748ZQDcbfyVy_1749053865016"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053865016"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053865016"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T16:17:45.022Z"}
 LOG  [PostingDetail][Init] Starting coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 8776, "timestamp": "2025-06-04T16:17:45.022Z"}
 LOG  [PostingDetail][Init] Starting centralized coordination {"postingId": "QWWEivga748ZQDcbfyVy", "timeSinceStart": 8777, "timestamp": "2025-06-04T16:17:45.023Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_QWWEivga748ZQDcbfyVy_1749053865023", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_QWWEivga748ZQDcbfyVy_1749053865023", "type": "offers_list"}
 LOG  [PostingDetail][Init] Coordination complete {"offersSubId": "offers_QWWEivga748ZQDcbfyVy_1749053865023", "postingSubId": "posting_QWWEivga748ZQDcbfyVy_1749053865023", "totalTime": 8777}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "QWWEivga748ZQDcbfyVy", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749053865017, "offersCount": 1, "postingDetailsId": "QWWEivga748ZQDcbfyVy", "userOffer": null}, "timestamp": "2025-06-04T16:17:45.023Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 6}, "props": {"postingId": "QWWEivga748ZQDcbfyVy", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749053865017, "offersCount": 1, "postingDetailsId": "QWWEivga748ZQDcbfyVy", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T16:17:36.246Z", "subscriptionCount": 0, "totalTime": 8777}, "types": []}, "timestamp": "2025-06-04T16:17:45.023Z"}





 On Simulator 2: a notification was received regarding to the deletion of the posting and notification bell had a red dot. when i tapped the notification bell icon in home screen, i navigated to the notification screen. i was able to see the new notification and tapped on it, it navigated to the offer detail screen. the offer detail screen was not displayed correctly with deletion info as the offer owner was ALLOWED to send discussion message and the withdrawal banner was NOT displayed. in a correct implementation, the offer owner should not be allowed to send discussion message and the withdrawal banner should be displayed as mentioned in the user journey.





 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Notifications
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [BackButton] Rendering back button
 LOG  Setting up notifications subscription
 LOG  [notificationService] Setting up notification subscription
 LOG  Notification snapshot received, count: 1
 LOG  Received 1 notifications
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  Notification snapshot received, count: 437
 LOG  Received 437 notifications
 LOG  [NotificationsScreen][handleNotificationPress] Starting with: {"hasData": true, "notificationId": "BmgyWY2aOYmQ1tFLyjcm", "offerId": "Zzo2UV9W0vDn32fEp9of", "stack": "Error
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:272584:29)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5917:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5931:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5936:14)
    at tryCallTwo (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:61:9)
    at doResolve (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:216:25)
    at Promise (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:82:14)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5928:25)
    at apply (native)
    at handleNotificationPress (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:272694:27)
    at onPress (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:272729:41)
    at _performTransitionSideEffects (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79291:22)
    at _receiveSignal (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79241:45)
    at onResponderRelease (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79097:34)
    at apply (native)
    at invokeGuardedCallbackProd (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79798:21)
    at apply (native)
    at invokeGuardedCallback (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79974:42)
    at apply (native)
    at invokeGuardedCallbackAndCatchFirstError (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79988:36)
    at executeDispatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:80065:48)
    at executeDispatchesInOrder (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:80087:26)
    at executeDispatchesAndRelease (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81858:35)
    at executeDispatchesAndReleaseTopLevel (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81865:43)
    at forEach (native)
    at forEachAccumulated (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:80679:22)
    at runEventsInBatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81878:27)
    at runExtractedPluginEventsInBatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81988:25)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81959:42)
    at batchedUpdates$1 (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:95400:20)
    at batchedUpdates (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81834:36)
    at _receiveRootNodeIDEvent (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81958:23)
    at receiveTouches (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:82047:34)
    at apply (native)
    at __callFunction (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3612:36)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3334:31)
    at __guard (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3551:15)
    at callFunctionReturnFlushedQueue (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3333:21)", "type": "SYSTEM_WITHDRAWAL"}
 LOG  Notification snapshot received, count: 437
 LOG  Received 437 notifications
 LOG  [NotificationsScreen] Processing SYSTEM_WITHDRAWAL notification: {"notification": {"createdAt": [Object], "id": "BmgyWY2aOYmQ1tFLyjcm", "offerId": "Zzo2UV9W0vDn32fEp9of", "postingId": "QWWEivga748ZQDcbfyVy", "read": false, "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "type": "SYSTEM_WITHDRAWAL"}, "timestamp": "2025-06-04T16:18:19.239Z"}
 LOG  [NotificationsScreen] Navigating to OfferDetail for withdrawn offer: {"offerId": "Zzo2UV9W0vDn32fEp9of", "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:18:19.240Z", "type": "SYSTEM_WITHDRAWAL"}
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Notifications
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: OfferDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [BackButton] Rendering back button
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": null, "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-04T16:18:19.284Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": null, "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T16:18:19.285Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": null, "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": false, "offerOwnerId": null, "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T16:18:19.290Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": null, "shouldShowAbusiveButton": false, "timestamp": "2025-06-04T16:18:19.301Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.301Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.301Z"}
 LOG  [OfferCache][GET] {"age": 53942, "found": true, "isExpired": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.302Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.303Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.303Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.303Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.303Z"}
 LOG  [OfferCache][GET] {"age": 53944, "found": true, "isExpired": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.304Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.304Z"}
 LOG  [useAuthUser] Setting up auth state listener {"currentUser": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "1731681044939", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "1749053684426", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "hasAuth": true, "hasOnAuthStateChanged": true}
 LOG  [OfferDetail][MOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "MOUNT", "mountCount": 0, "params": {"offerId": "Zzo2UV9W0vDn32fEp9of", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "QWWEivga748ZQDcbfyVy", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "timestamp": "2025-06-04T16:18:19.311Z"}
 LOG  [Messages Subscription] Setting up subscription for offerId: Zzo2UV9W0vDn32fEp9of
 LOG  === OfferDetail Screen Mounted ===
 LOG  Route params: {"offerId": "Zzo2UV9W0vDn32fEp9of", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "QWWEivga748ZQDcbfyVy", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  Setting owner IDs from route params: {"offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [OfferDetail][MOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "MOUNT", "mountCount": 1, "params": {"offerId": "Zzo2UV9W0vDn32fEp9of", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "QWWEivga748ZQDcbfyVy", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "timestamp": "2025-06-04T16:18:19.312Z"}
 LOG  Authenticated user: ybEyGDxbvleCEXdTvrA2CgVTJ1y2
 LOG  [OfferDetail] Initializing with params: {"hasInitialOffer": false, "hasInitialPosting": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.312Z"}
 LOG  [OfferDetail] Setting postingOwnerId from route params: {"postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [OfferDetail] Setting offerOwnerId from route params: {"offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  OfferDetail refresh button states: {"showOfferRefresh": false, "showOfferRefreshExplicit": false, "showPostingRefresh": false, "showPostingRefreshExplicit": false, "timestamp": "2025-06-04T16:18:19.313Z"}
 LOG  [OfferDetail][NOTIFICATION_LISTENER_SETUP] {"offerId": "Zzo2UV9W0vDn32fEp9of", "postingId": undefined, "timestamp": "2025-06-04T16:18:19.313Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_LISTENER_SETUP] {"offerId": "Zzo2UV9W0vDn32fEp9of", "postingId": undefined, "timestamp": "2025-06-04T16:18:19.313Z", "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "kous surgical fix test", "timestamp": "2025-06-04T16:18:19.315Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T16:18:19.316Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "111", "offerStatus": "pending", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T16:18:19.320Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.329Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.329Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.329Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.329Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.329Z"}
 LOG  [OfferDetail][NOTIFICATION_LISTENER_CLEANUP] {"offerId": "Zzo2UV9W0vDn32fEp9of", "postingId": undefined, "timestamp": "2025-06-04T16:18:19.329Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-04T16:18:19.330Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.330Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.330Z"}
 LOG  [OfferCache][GET] {"age": 53970, "found": true, "isExpired": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.330Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.330Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.330Z"}
 LOG  [OfferDetail][NOTIFICATION_LISTENER_SETUP] {"offerId": "Zzo2UV9W0vDn32fEp9of", "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:18:19.331Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_LISTENER_SETUP] {"offerId": "Zzo2UV9W0vDn32fEp9of", "postingId": "QWWEivga748ZQDcbfyVy", "timestamp": "2025-06-04T16:18:19.331Z", "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "kous surgical fix test", "timestamp": "2025-06-04T16:18:19.333Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T16:18:19.333Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "111", "offerStatus": "pending", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T16:18:19.337Z"}
 LOG  [useAuthUser] Auth state changed {"user": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "1731681044939", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "1749053684426", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.376Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.377Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.377Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.377Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.377Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-04T16:18:19.377Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.378Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.378Z"}
 LOG  [OfferCache][GET] {"age": 54018, "found": true, "isExpired": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.378Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.378Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.378Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hots offer", "hookPrice": 111, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "111", "offerStatus": "pending", "restrictionEndDate": null, "userScore": undefined, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T16:18:19.381Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hots offer", "offerId": "Zzo2UV9W0vDn32fEp9of", "price": 111, "timestamp": "2025-06-04T16:18:19.385Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 0, "timestamp": "2025-06-04T16:18:19.393Z"}
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.81134209034643, "latitudeDelta": 0.01976623355351137, "longitude": -122.41817964989167, "longitudeDelta": 0.01609314918364646}
 LOG  [Messages Subscription] Received snapshot update: {"empty": false, "metadata": {"fromCache": false, "hasPendingWrites": false}, "size": 1}
 LOG  [Messages Subscription] Processing discussion: {"id": "WX58ShLpzxC8IamUQWDc", "messageCount": 1}
 LOG  [Messages Subscription] Raw messages: {"count": 1, "messageKeys": ["senderId", "read", "type", "timestamp", "text"], "sampleMessage": {"read": true, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749053864}, "type": "system"}}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["senderId", "read", "type", "timestamp", "text"], "timestampType": "object"}
 LOG  [Messages Subscription] Validation summary: {"firstValidMessage": {"id": undefined, "senderId": "system", "timestamp": {"nanoseconds": 0, "seconds": 1749053864}}, "invalidMessages": 0, "totalMessages": 1, "validMessages": 1}
 LOG  [Messages Subscription] Processing messages: {"hasLegacyMessages": true, "messageTypes": [{"hasId": false, "hasTimestamp": true, "timestampType": "object"}], "newCount": 1, "prevCount": 0}
 LOG  [Messages Subscription] Updated messages state: {"firstMessage": {"id": undefined, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749053864}}, "hasLegacyMessages": true, "lastMessage": {"id": undefined, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749053864}}, "newCount": 1, "previousCount": 0}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "kous surgical fix test", "timestamp": "2025-06-04T16:18:19.512Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T16:18:19.512Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "111", "offerStatus": "pending", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T16:18:19.517Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.530Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.531Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.531Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.531Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.531Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-04T16:18:19.531Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.532Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.532Z"}
 LOG  [OfferCache][GET] {"age": 54172, "found": true, "isExpired": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.532Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.532Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.532Z"}
 LOG  Marking messages as read for authorized user: {"isOfferOwner": true, "isPostingOwner": false, "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  Initial load, scrolling to end: 1
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hots offer", "hookPrice": 111, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "111", "offerStatus": "pending", "restrictionEndDate": null, "userScore": undefined, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T16:18:19.535Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hots offer", "offerId": "Zzo2UV9W0vDn32fEp9of", "price": 111, "timestamp": "2025-06-04T16:18:19.538Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "kous surgical fix test", "timestamp": "2025-06-04T16:18:19.548Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T16:18:19.548Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "111", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T16:18:19.552Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.564Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.564Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.564Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.565Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.565Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-04T16:18:19.565Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.565Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.565Z"}
 LOG  [OfferCache][GET] {"age": 54206, "found": true, "isExpired": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.566Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.566Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.566Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hots offer", "hookPrice": 111, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "111", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T16:18:19.568Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hots offer", "offerId": "Zzo2UV9W0vDn32fEp9of", "price": 111, "timestamp": "2025-06-04T16:18:19.571Z"}
 LOG  FlatList layout complete
 LOG  Skipping load more: {"hasLastDoc": false, "hasMoreMessages": true, "lastDocId": undefined, "loadingMore": false}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "kous surgical fix test", "timestamp": "2025-06-04T16:18:19.657Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T16:18:19.657Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "111", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T16:18:19.663Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.674Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.675Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.675Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.675Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.675Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-04T16:18:19.676Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.676Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.676Z"}
 LOG  [OfferCache][GET] {"age": 54316, "found": true, "isExpired": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.676Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.676Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "Zzo2UV9W0vDn32fEp9of", "timestamp": "2025-06-04T16:18:19.676Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hots offer", "hookPrice": 111, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "111", "offerStatus": "pending", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T16:18:19.678Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hots offer", "offerId": "Zzo2UV9W0vDn32fEp9of", "price": 111, "timestamp": "2025-06-04T16:18:19.682Z"}
 LOG  Debounced region changed: {"latitude": 37.81134209034643, "latitudeDelta": 0.01976623355351137, "longitude": -122.41817964989167, "longitudeDelta": 0.01609314918364646}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.81134209034643, "latitudeDelta": 0.01976623355351137, "longitude": -122.41817964989167, "longitudeDelta": 0.01609314918364646}
 LOG  [fetchPostingsBySearch] Query results: 2
 LOG  [useControlledPostings] Fetched postings: 2
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true

