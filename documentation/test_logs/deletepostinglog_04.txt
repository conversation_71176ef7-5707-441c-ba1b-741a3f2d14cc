Setup Two Simulators:
Simulator 1: Posting owner 
Simulator 2: Offer owner 

Create Test Data:
On Simulator 1: Create a new posting with a distinctive title -done






 LOG  Posting added with ID: 57F81UMKDYW9nl6hU9kI
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: CreatePosting
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: CreatePosting
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.804258629178044, "latitudeDelta": 0.031302722365168734, "longitude": -122.41320067577597, "longitudeDelta": 0.019613941535808976}
 LOG  Debounced region changed: {"latitude": 37.804258629178044, "latitudeDelta": 0.031302722365168734, "longitude": -122.41320067577597, "longitudeDelta": 0.019613941535808976}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.804258629178044, "latitudeDelta": 0.031302722365168734, "longitude": -122.41320067577597, "longitudeDelta": 0.019613941535808976}
 LOG  [fetchPostingsBySearch] Query results: 6
 LOG  [useControlledPostings] Fetched postings: 6
 LOG  [useAuthUser] Cleaning up auth state listener


On Simulator 2: Submit an offer on that posting -done (there's an issue at this step that when the offer owner succesfully submits the offer and navigated to posting detail screen, the newly submitted offer is not displayed under active offers tab. as the offer is not displayed, the offer owner can see the make offer button which is not correct. if a user submits an offer, they should not be allowed to make another offer while they have an active offer)

then i navigated away from posting detail screen to home screen and navigated back to posting detail screen, the offer was displayed under active offers tab correctly (no log for this below)




 LOG  [PostingDetail][Navigation] Attempting safe navigation
 LOG  [PostingDetail][Navigation] Checking listeners: {"activeCount": 2, "byCollection": {"offers": 1, "postings": 1}, "timestamp": "2025-06-04T12:23:13.473Z"}
 LOG  [PostingDetail][Navigation] Cleaning up hooks
 LOG  [PostingDetail] Unsubscribing from posting
 LOG  [usePostingDetails][Unsubscribe] Cleaning up listener: {"listenerId": "postings_1749039782474_1749039782474", "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:23:13.474Z"}
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_57F81UMKDYW9nl6hU9kI", "listenerId": "postings_1749039782474_1749039782474", "timestamp": "2025-06-04T12:23:13.474Z"}
 LOG  [PostingDetail] Successfully unsubscribed from posting
 LOG  [PostingDetail] Unsubscribing from offers
 LOG  [useOffers][Unsubscribe] Cleaning up subscription: {"postingId": "57F81UMKDYW9nl6hU9kI", "subscriptionId": "offers_1749039782475_1749039782475", "timestamp": "2025-06-04T12:23:13.475Z"}
 LOG  [ListenerRegistry][Component] Unregistered listener from component: {"componentId": "PostingDetail_57F81UMKDYW9nl6hU9kI", "listenerId": "offers_1749039782475_1749039782475", "timestamp": "2025-06-04T12:23:13.477Z"}
 LOG  [PostingDetail] Successfully unsubscribed from offers
 LOG  [ListenerRegistry] Cleanup metrics: {"groupId": null, "metrics": {"duration": 5, "startTime": 1749039793475, "success": true}, "subscriptionId": "postings_1749039782474_1749039782474", "timestamp": "2025-06-04T12:23:13.480Z"}
 LOG  [ListenerRegistry] Cleanup metrics: {"groupId": null, "metrics": {"duration": 6, "startTime": 1749039793478, "success": true}, "subscriptionId": "offers_1749039782475_1749039782475", "timestamp": "2025-06-04T12:23:13.484Z"}
 LOG  [PostingDetail][Navigation] Post-cleanup verification: {"activeListeners": 0, "byCollection": {"offers": 0, "postings": 0}, "timestamp": "2025-06-04T12:23:13.586Z"}
 LOG  [PostingDetail][Navigation] All listeners cleaned up successfully
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: MakeOffer
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: MakeOffer
 LOG  [Navigation] Can go back status: true
 LOG  [BackButton] Rendering back button
 LOG  [MakeOffer][Verify] Checking for active listeners: {"stats": {"activeCleanups": 0, "activeScreens": Set {}, "byCollection": {"offers": 0, "postings": 0}, "lastCleanup": null, "timestamp": "2025-06-04T12:23:13.655Z", "totalListeners": 0}, "timestamp": "2025-06-04T12:23:13.655Z"}
 LOG  [MakeOffer] Checking user restrictions for: ybEyGDxbvleCEXdTvrA2CgVTJ1y2
 LOG  [Navigation] Rendering headerLeft for: MakeOffer
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: MakeOffer
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.813802134300765, "latitudeDelta": 0.019765574962200105, "longitude": -122.42102023001881, "longitudeDelta": 0.01609314918363225}
 LOG  Debounced region changed: {"latitude": 37.813802134300765, "latitudeDelta": 0.019765574962200105, "longitude": -122.42102023001881, "longitudeDelta": 0.01609314918363225}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.813802134300765, "latitudeDelta": 0.019765574962200105, "longitude": -122.42102023001881, "longitudeDelta": 0.01609314918363225}
 LOG  [fetchPostingsBySearch] Query results: 2
 LOG  [useControlledPostings] Fetched postings: 2
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: MakeOffer
 LOG  [Navigation] Can go back status: true
 LOG  [MakeOffer][Verify] Checking for active listeners: {"stats": {"activeCleanups": 0, "activeScreens": Set {}, "byCollection": {"offers": 0, "postings": 0}, "lastCleanup": null, "timestamp": "2025-06-04T12:23:18.670Z", "totalListeners": 0}, "timestamp": "2025-06-04T12:23:18.670Z"}
 LOG  [MakeOffer][Verify] Checking for active listeners: {"stats": {"activeCleanups": 0, "activeScreens": Set {}, "byCollection": {"offers": 0, "postings": 0}, "lastCleanup": null, "timestamp": "2025-06-04T12:23:23.687Z", "totalListeners": 0}, "timestamp": "2025-06-04T12:23:23.687Z"}
 LOG  [MakeOffer][Verify] Checking for active listeners: {"stats": {"activeCleanups": 0, "activeScreens": Set {}, "byCollection": {"offers": 0, "postings": 0}, "lastCleanup": null, "timestamp": "2025-06-04T12:23:28.688Z", "totalListeners": 0}, "timestamp": "2025-06-04T12:23:28.688Z"}
 LOG  [MakeOffer][Verify] Checking for active listeners: {"stats": {"activeCleanups": 0, "activeScreens": Set {}, "byCollection": {"offers": 0, "postings": 0}, "lastCleanup": null, "timestamp": "2025-06-04T12:23:33.689Z", "totalListeners": 0}, "timestamp": "2025-06-04T12:23:33.689Z"}
 LOG  [MakeOffer] Submitting offer: {"description": "hots offer", "postingId": "57F81UMKDYW9nl6hU9kI", "price": "414"}
 LOG  [MakeOffer] Creating offer with data: {"postingId": "57F81UMKDYW9nl6hU9kI", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "price": 414}
 LOG  [addOfferAndDiscussion] Starting with data: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "57F81UMKDYW9nl6hU9kI", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "price": 414, "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  [addOfferAndDiscussion] Offer created: {"offerId": "IxPWHv5aJN5qViIRlCho"}
 LOG  [addOfferAndDiscussion] Discussion created: {"discussionId": "mzxZDg3AlMCbjwAehE9X"}
 LOG  [addOfferAndDiscussion] Creating notification: {"body": "You received a new offer of $414", "createdAt": {"_methodName": "serverTimestamp"}, "offerId": "IxPWHv5aJN5qViIRlCho", "postingId": "57F81UMKDYW9nl6hU9kI", "read": false, "recipientId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "senderId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "title": "New Offer", "type": "NEW_OFFER"}
 LOG  [addOfferAndDiscussion] Notification created successfully
 LOG  [addOfferAndDiscussion] Updated user favorites
 LOG  [MakeOffer] Offer created successfully: {"offerId": "IxPWHv5aJN5qViIRlCho"}
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: PostingDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: MakeOffer
 LOG  [Navigation] Can go back status: true
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:23:02.454Z"}, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:23:35.438Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:23:35.440Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:23:35.440Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 8, "subscriptions": ["posting_57F81UMKDYW9nl6hU9kI_1749039782683", "offers_57F81UMKDYW9nl6hU9kI_1749039782683", "posting_57F81UMKDYW9nl6hU9kI_1749039782719", "offers_57F81UMKDYW9nl6hU9kI_1749039782719", "posting_57F81UMKDYW9nl6hU9kI_1749039782720", "offers_57F81UMKDYW9nl6hU9kI_1749039782720", "posting_57F81UMKDYW9nl6hU9kI_1749039782736", "offers_57F81UMKDYW9nl6hU9kI_1749039782736"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749039782683"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749039782683"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749039782719"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749039782719"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749039782720"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749039782720"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749039782736"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749039782736"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:23:35.456Z"}
 LOG  [PostingDetail][Init] Starting initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 33002, "timestamp": "2025-06-04T12:23:35.456Z"}
 LOG  [PostingDetail][Init] Starting centralized initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 33002, "timestamp": "2025-06-04T12:23:35.456Z"}
 LOG  [useFavorites][Focus] Screen focused, fetching favorite status
 LOG  [PostingDetail][Focus] Screen focused - debouncing initialization
 LOG  [PostingDetail][Focus] Cleared previous focus timeout
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:23:02.454Z"}, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:23:35.458Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:23:35.459Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:23:35.460Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:23:35.467Z"}
 LOG  [PostingDetail][Init] Starting initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 33013, "timestamp": "2025-06-04T12:23:35.467Z"}
 LOG  [PostingDetail][Init] Starting centralized initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 33014, "timestamp": "2025-06-04T12:23:35.468Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749039815519", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749039815519", "type": "offers_list"}
 LOG  [PostingDetail][Init] Centralized setup complete {"offersSubId": "offers_57F81UMKDYW9nl6hU9kI_1749039815519", "postingSubId": "posting_57F81UMKDYW9nl6hU9kI_1749039815519", "totalTime": 33065}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749039815520", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749039815520", "type": "offers_list"}
 LOG  [PostingDetail][Init] Centralized setup complete {"offersSubId": "offers_57F81UMKDYW9nl6hU9kI_1749039815520", "postingSubId": "posting_57F81UMKDYW9nl6hU9kI_1749039815520", "totalTime": 33066}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:23:02.454Z"}, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:23:35.574Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:23:35.574Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:23:35.575Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 4, "subscriptions": ["posting_57F81UMKDYW9nl6hU9kI_1749039815519", "offers_57F81UMKDYW9nl6hU9kI_1749039815519", "posting_57F81UMKDYW9nl6hU9kI_1749039815520", "offers_57F81UMKDYW9nl6hU9kI_1749039815520"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749039815519"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749039815519"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749039815520"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749039815520"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:23:35.584Z"}
 LOG  [PostingDetail][Init] Starting initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 33130, "timestamp": "2025-06-04T12:23:35.584Z"}
 LOG  [PostingDetail][Init] Starting centralized initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 33130, "timestamp": "2025-06-04T12:23:35.584Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:23:02.454Z"}, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:23:35.585Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:23:35.585Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:23:35.585Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:23:35.591Z"}
 LOG  [PostingDetail][Init] Starting initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 33137, "timestamp": "2025-06-04T12:23:35.591Z"}
 LOG  [PostingDetail][Init] Starting centralized initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 33137, "timestamp": "2025-06-04T12:23:35.591Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749039815635", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749039815635", "type": "offers_list"}
 LOG  [PostingDetail][Init] Centralized setup complete {"offersSubId": "offers_57F81UMKDYW9nl6hU9kI_1749039815635", "postingSubId": "posting_57F81UMKDYW9nl6hU9kI_1749039815635", "totalTime": 33182}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749039815652", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749039815652", "type": "offers_list"}
 LOG  [PostingDetail][Init] Centralized setup complete {"offersSubId": "offers_57F81UMKDYW9nl6hU9kI_1749039815652", "postingSubId": "posting_57F81UMKDYW9nl6hU9kI_1749039815652", "totalTime": 33198}
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: PostingDetail
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.81380213411789, "latitudeDelta": 0.021935380804087856, "longitude": -122.42102023001881, "longitudeDelta": 0.01609314918363225}
 LOG  Debounced region changed: {"latitude": 37.81380213411789, "latitudeDelta": 0.021935380804087856, "longitude": -122.42102023001881, "longitudeDelta": 0.01609314918363225}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.81380213411789, "latitudeDelta": 0.021935380804087856, "longitude": -122.42102023001881, "longitudeDelta": 0.01609314918363225}
 LOG  [fetchPostingsBySearch] Query results: 2
 LOG  [useControlledPostings] Fetched postings: 2






Execute Test:
On Simulator 1: Delete the posting and observe logs






 LOG  === Starting Posting Deletion Flow ===
 LOG  PostingId: 57F81UMKDYW9nl6hU9kI
 LOG  [deletePosting] Starting deletion process {"postingId": "57F81UMKDYW9nl6hU9kI", "stack": "Error
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:193399:25)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5917:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5931:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5936:14)
    at tryCallTwo (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:61:9)
    at doResolve (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:216:25)
    at Promise (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:82:14)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5928:25)
    at apply (native)
    at deletePosting (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:193636:26)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:261386:56)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5917:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5931:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5936:14)
    at tryCallTwo (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:61:9)
    at doResolve (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:216:25)
    at Promise (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:82:14)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5928:25)
    at apply (native)
    at onPress (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:261401:34)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:42025:21)
    at apply (native)
    at __invokeCallback (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3648:23)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3343:34)
    at __guard (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3551:15)
    at invokeCallbackAndReturnFlushedQueue (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3342:21)", "timestamp": "2025-06-04T12:29:50.280Z"}
 LOG  [deletePosting] Fetching active offers...
 LOG  [deletePosting] Found offers: {"count": 1, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.371Z"}
 LOG  [deletePosting] Marking posting as deleted: {"postingId": "57F81UMKDYW9nl6hU9kI", "postingTitle": "kous post to be deleted", "timestamp": "2025-06-04T12:29:50.374Z"}
 LOG  [deletePosting] Processing offer: {"offerId": "IxPWHv5aJN5qViIRlCho", "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.374Z"}
 LOG  [deletePosting] Added system message to discussion: {"discussionId": "mzxZDg3AlMCbjwAehE9X", "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:29:50.530Z"}
 LOG  [deletePosting] Creating notification for offer owner: {"offerId": "IxPWHv5aJN5qViIRlCho", "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "timestamp": "2025-06-04T12:29:50.531Z", "type": "SYSTEM_WITHDRAWAL"}
 LOG  [deletePosting] Committing batch updates...
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:29:37.873Z"}, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.543Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": false, "postingDetails": {"createdAt": [Timestamp], "description": "test", "id": "57F81UMKDYW9nl6hU9kI", "latitude": 37.813568990359386, "longitude": -122.42203819041593, "postingStatus": "Active", "searchTerms": [Array], "title": "kous post to be deleted", "titleLower": "kous post to be deleted", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 12671, "timestamp": "2025-06-04T12:29:50.544Z", "updateCount": 3}, "type": "UPDATE_POSTING"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:29:50.548Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.549Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 10, "subscriptions": ["posting_57F81UMKDYW9nl6hU9kI_1749040178080", "offers_57F81UMKDYW9nl6hU9kI_1749040178080", "posting_57F81UMKDYW9nl6hU9kI_1749040178101", "offers_57F81UMKDYW9nl6hU9kI_1749040178101", "posting_57F81UMKDYW9nl6hU9kI_1749040178117", "offers_57F81UMKDYW9nl6hU9kI_1749040178117", "posting_57F81UMKDYW9nl6hU9kI_1749040178118", "offers_57F81UMKDYW9nl6hU9kI_1749040178118", "posting_57F81UMKDYW9nl6hU9kI_1749040178134", "offers_57F81UMKDYW9nl6hU9kI_1749040178134"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749040178080"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749040178080"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749040178101"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749040178101"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749040178117"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749040178117"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749040178118"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749040178118"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749040178134"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749040178134"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:29:50.563Z"}
 LOG  [PostingDetail][Init] Starting initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 12690, "timestamp": "2025-06-04T12:29:50.563Z"}
 LOG  [PostingDetail][Init] Starting centralized initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 12690, "timestamp": "2025-06-04T12:29:50.563Z"}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T12:29:50.563Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.564Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T12:29:50.565Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.565Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "57F81UMKDYW9nl6hU9kI", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749040178056, "offersCount": 1, "postingDetailsId": "57F81UMKDYW9nl6hU9kI", "userOffer": null}, "timestamp": "2025-06-04T12:29:50.565Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": true, "isOwner": true, "timestamp": "2025-06-04T12:29:50.566Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:29:37.873Z"}, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.567Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:29:50.567Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.568Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:29:50.578Z"}
 LOG  [PostingDetail][Init] Starting initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 12705, "timestamp": "2025-06-04T12:29:50.578Z"}
 LOG  [PostingDetail][Init] Starting centralized initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 12705, "timestamp": "2025-06-04T12:29:50.578Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "57F81UMKDYW9nl6hU9kI", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749040190567, "offersCount": 1, "postingDetailsId": "57F81UMKDYW9nl6hU9kI", "userOffer": null}, "timestamp": "2025-06-04T12:29:50.578Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 12}, "props": {"postingId": "57F81UMKDYW9nl6hU9kI", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749040190567, "offersCount": 1, "postingDetailsId": "57F81UMKDYW9nl6hU9kI", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T12:29:37.873Z", "subscriptionCount": 0, "totalTime": 12706}, "types": []}, "timestamp": "2025-06-04T12:29:50.579Z"}
 LOG  [useOffers][Update] {"changes": [{"id": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:29:50.580Z", "type": "modified"}], "count": 1, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.580Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:29:37.873Z"}, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.581Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:29:50.582Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.582Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:29:50.588Z"}
 LOG  [PostingDetail][Init] Starting initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 12715, "timestamp": "2025-06-04T12:29:50.588Z"}
 LOG  [PostingDetail][Init] Starting centralized initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 12716, "timestamp": "2025-06-04T12:29:50.589Z"}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T12:29:50.589Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.589Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T12:29:50.589Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.589Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:29:37.873Z"}, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.590Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:29:50.591Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.592Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:29:50.598Z"}
 LOG  [PostingDetail][Init] Starting initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 12725, "timestamp": "2025-06-04T12:29:50.598Z"}
 LOG  [PostingDetail][Init] Starting centralized initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 12725, "timestamp": "2025-06-04T12:29:50.598Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "57F81UMKDYW9nl6hU9kI", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749040190590, "offersCount": 1, "postingDetailsId": "57F81UMKDYW9nl6hU9kI", "userOffer": null}, "timestamp": "2025-06-04T12:29:50.598Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 9}, "props": {"postingId": "57F81UMKDYW9nl6hU9kI", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749040190590, "offersCount": 1, "postingDetailsId": "57F81UMKDYW9nl6hU9kI", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T12:29:37.873Z", "subscriptionCount": 0, "totalTime": 12726}, "types": []}, "timestamp": "2025-06-04T12:29:50.599Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749040190618", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749040190618", "type": "offers_list"}
 LOG  [PostingDetail][Init] Centralized setup complete {"offersSubId": "offers_57F81UMKDYW9nl6hU9kI_1749040190618", "postingSubId": "posting_57F81UMKDYW9nl6hU9kI_1749040190618", "totalTime": 12745}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749040190634", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749040190634", "type": "offers_list"}
 LOG  [PostingDetail][Init] Centralized setup complete {"offersSubId": "offers_57F81UMKDYW9nl6hU9kI_1749040190634", "postingSubId": "posting_57F81UMKDYW9nl6hU9kI_1749040190634", "totalTime": 12762}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749040190651", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749040190651", "type": "offers_list"}
 LOG  [PostingDetail][Init] Centralized setup complete {"offersSubId": "offers_57F81UMKDYW9nl6hU9kI_1749040190651", "postingSubId": "posting_57F81UMKDYW9nl6hU9kI_1749040190651", "totalTime": 12778}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749040190652", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749040190652", "type": "offers_list"}
 LOG  [PostingDetail][Init] Centralized setup complete {"offersSubId": "offers_57F81UMKDYW9nl6hU9kI_1749040190652", "postingSubId": "posting_57F81UMKDYW9nl6hU9kI_1749040190652", "totalTime": 12779}
 LOG  [deletePosting] Notification document created: {"notificationId": "iEVT2PJ4jE8h4CG53rmF", "offerId": "IxPWHv5aJN5qViIRlCho", "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "timestamp": "2025-06-04T12:29:50.687Z"}
 LOG  [deletePosting] Sending notifications to offer owners... {"notificationCount": 1, "timestamp": "2025-06-04T12:29:50.752Z"}
 LOG  [deletePosting] Notification results: {"fulfilled": 1, "rejected": 0, "timestamp": "2025-06-04T12:29:50.753Z", "total": 1}
 LOG  [deletePosting] Notification 1 succeeded: {"notificationId": "iEVT2PJ4jE8h4CG53rmF", "timestamp": "2025-06-04T12:29:50.753Z"}
 LOG  [deletePosting] Process completed successfully: {"notificationsSent": 1, "offersProcessed": 1, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.753Z"}
 LOG  Posting deleted successfully
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:29:37.873Z"}, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.764Z"}
 LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": false, "postingDetails": {"createdAt": [Timestamp], "deletedAt": null, "description": "test", "id": "57F81UMKDYW9nl6hU9kI", "latitude": 37.813568990359386, "longitude": -122.42203819041593, "postingStatus": "Deleted", "searchTerms": [Array], "title": "kous post to be deleted", "titleLower": "kous post to be deleted", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 12891, "timestamp": "2025-06-04T12:29:50.764Z", "updateCount": 4}, "type": "UPDATE_POSTING"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:29:50.765Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.766Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 8, "subscriptions": ["posting_57F81UMKDYW9nl6hU9kI_1749040190618", "offers_57F81UMKDYW9nl6hU9kI_1749040190618", "posting_57F81UMKDYW9nl6hU9kI_1749040190634", "offers_57F81UMKDYW9nl6hU9kI_1749040190634", "posting_57F81UMKDYW9nl6hU9kI_1749040190651", "offers_57F81UMKDYW9nl6hU9kI_1749040190651", "posting_57F81UMKDYW9nl6hU9kI_1749040190652", "offers_57F81UMKDYW9nl6hU9kI_1749040190652"]}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749040190618"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749040190618"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749040190634"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749040190634"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749040190651"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749040190651"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749040190652"}
 LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749040190652"}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:29:50.774Z"}
 LOG  [PostingDetail][Init] Starting initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 12902, "timestamp": "2025-06-04T12:29:50.775Z"}
 LOG  [PostingDetail][Init] Starting centralized initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 12902, "timestamp": "2025-06-04T12:29:50.775Z"}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T12:29:50.775Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.775Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T12:29:50.776Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.776Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "57F81UMKDYW9nl6hU9kI", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749040190590, "offersCount": 1, "postingDetailsId": "57F81UMKDYW9nl6hU9kI", "userOffer": null}, "timestamp": "2025-06-04T12:29:50.776Z"}
 LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": true, "isOwner": true, "timestamp": "2025-06-04T12:29:50.776Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:29:37.873Z"}, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.778Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:29:50.778Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.779Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:29:50.787Z"}
 LOG  [PostingDetail][Init] Starting initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 12914, "timestamp": "2025-06-04T12:29:50.787Z"}
 LOG  [PostingDetail][Init] Starting centralized initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 12914, "timestamp": "2025-06-04T12:29:50.787Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "57F81UMKDYW9nl6hU9kI", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749040190778, "offersCount": 1, "postingDetailsId": "57F81UMKDYW9nl6hU9kI", "userOffer": null}, "timestamp": "2025-06-04T12:29:50.788Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 10}, "props": {"postingId": "57F81UMKDYW9nl6hU9kI", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749040190778, "offersCount": 1, "postingDetailsId": "57F81UMKDYW9nl6hU9kI", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T12:29:37.873Z", "subscriptionCount": 0, "totalTime": 12915}, "types": []}, "timestamp": "2025-06-04T12:29:50.788Z"}
 LOG  [useOffers][Update] {"changes": [{"id": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:29:50.789Z", "type": "modified"}], "count": 1, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.789Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:29:37.873Z"}, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.790Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:29:50.791Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.791Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:29:50.796Z"}
 LOG  [PostingDetail][Init] Starting initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 12923, "timestamp": "2025-06-04T12:29:50.796Z"}
 LOG  [PostingDetail][Init] Starting centralized initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 12923, "timestamp": "2025-06-04T12:29:50.796Z"}
 LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T12:29:50.796Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.796Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T12:29:50.797Z", "userOffers": 0}
 LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.797Z"}
 LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T12:29:37.873Z"}, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.798Z"}
 LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T12:29:50.798Z"}
 LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:29:50.798Z", "userOffer": null}
 LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 0, "subscriptions": []}
 LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T12:29:50.805Z"}
 LOG  [PostingDetail][Init] Starting initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 12932, "timestamp": "2025-06-04T12:29:50.805Z"}
 LOG  [PostingDetail][Init] Starting centralized initialization {"postingId": "57F81UMKDYW9nl6hU9kI", "timeSinceStart": 12932, "timestamp": "2025-06-04T12:29:50.805Z"}
 LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "57F81UMKDYW9nl6hU9kI", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749040190798, "offersCount": 1, "postingDetailsId": "57F81UMKDYW9nl6hU9kI", "userOffer": null}, "timestamp": "2025-06-04T12:29:50.805Z"}
 LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 7}, "props": {"postingId": "57F81UMKDYW9nl6hU9kI", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749040190798, "offersCount": 1, "postingDetailsId": "57F81UMKDYW9nl6hU9kI", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T12:29:37.873Z", "subscriptionCount": 0, "totalTime": 12932}, "types": []}, "timestamp": "2025-06-04T12:29:50.805Z"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749040190834", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749040190834", "type": "offers_list"}
 LOG  [PostingDetail][Init] Centralized setup complete {"offersSubId": "offers_57F81UMKDYW9nl6hU9kI_1749040190834", "postingSubId": "posting_57F81UMKDYW9nl6hU9kI_1749040190834", "totalTime": 12962}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749040190851", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749040190851", "type": "offers_list"}
 LOG  [PostingDetail][Init] Centralized setup complete {"offersSubId": "offers_57F81UMKDYW9nl6hU9kI_1749040190851", "postingSubId": "posting_57F81UMKDYW9nl6hU9kI_1749040190851", "totalTime": 12978}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749040190852", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749040190852", "type": "offers_list"}
 LOG  [PostingDetail][Init] Centralized setup complete {"offersSubId": "offers_57F81UMKDYW9nl6hU9kI_1749040190852", "postingSubId": "posting_57F81UMKDYW9nl6hU9kI_1749040190852", "totalTime": 12979}
 LOG  [PostingDetail][Listener] Tracking: {"id": "posting_57F81UMKDYW9nl6hU9kI_1749040190868", "type": "posting_details"}
 LOG  [PostingDetail][Listener] Tracking: {"id": "offers_57F81UMKDYW9nl6hU9kI_1749040190868", "type": "offers_list"}
 LOG  [PostingDetail][Init] Centralized setup complete {"offersSubId": "offers_57F81UMKDYW9nl6hU9kI_1749040190868", "postingSubId": "posting_57F81UMKDYW9nl6hU9kI_1749040190868", "totalTime": 12995}



 On Simulator 2: a notification was received regarding to the deletion of the posting and notification bell had a red dot. when i tapped the notification bell icon in home screen, i navigated to the notification screen. i was able to see the new notification and tapped on it, it navigated to the offer detail screen. the offer detail screen was displayed correctly with deletion info.



 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Notifications
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [BackButton] Rendering back button
 LOG  Setting up notifications subscription
 LOG  [notificationService] Setting up notification subscription
 LOG  Notification snapshot received, count: 1
 LOG  Received 1 notifications
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  Notification snapshot received, count: 434
 LOG  Received 434 notifications
 LOG  [NotificationsScreen][handleNotificationPress] Starting with: {"hasData": true, "notificationId": "iEVT2PJ4jE8h4CG53rmF", "offerId": "IxPWHv5aJN5qViIRlCho", "stack": "Error
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:272620:29)
    at next (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5917:19)
    at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5931:29)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5936:14)
    at tryCallTwo (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:61:9)
    at doResolve (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:216:25)
    at Promise (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:82:14)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5928:25)
    at apply (native)
    at handleNotificationPress (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:272730:27)
    at onPress (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:272765:41)
    at _performTransitionSideEffects (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79291:22)
    at _receiveSignal (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79241:45)
    at onResponderRelease (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79097:34)
    at apply (native)
    at invokeGuardedCallbackProd (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79798:21)
    at apply (native)
    at invokeGuardedCallback (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79974:42)
    at apply (native)
    at invokeGuardedCallbackAndCatchFirstError (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79988:36)
    at executeDispatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:80065:48)
    at executeDispatchesInOrder (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:80087:26)
    at executeDispatchesAndRelease (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81858:35)
    at executeDispatchesAndReleaseTopLevel (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81865:43)
    at forEach (native)
    at forEachAccumulated (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:80679:22)
    at runEventsInBatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81878:27)
    at runExtractedPluginEventsInBatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81988:25)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81959:42)
    at batchedUpdates$1 (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:95400:20)
    at batchedUpdates (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81834:36)
    at _receiveRootNodeIDEvent (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81958:23)
    at receiveTouches (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:82047:34)
    at apply (native)
    at __callFunction (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3612:36)
    at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3334:31)
    at __guard (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3551:15)
    at callFunctionReturnFlushedQueue (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3333:21)", "type": "SYSTEM_WITHDRAWAL"}
 LOG  Notification snapshot received, count: 434
 LOG  Received 434 notifications
 LOG  [NotificationsScreen] Processing SYSTEM_WITHDRAWAL notification: {"notification": {"createdAt": [Object], "id": "iEVT2PJ4jE8h4CG53rmF", "offerId": "IxPWHv5aJN5qViIRlCho", "postingId": "57F81UMKDYW9nl6hU9kI", "read": false, "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "type": "SYSTEM_WITHDRAWAL"}, "timestamp": "2025-06-04T12:30:32.173Z"}
 LOG  [NotificationsScreen] Navigating to OfferDetail for withdrawn offer: {"offerId": "IxPWHv5aJN5qViIRlCho", "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:30:32.174Z", "type": "SYSTEM_WITHDRAWAL"}
 LOG  [Navigation] Configuring screen: LoginScreen
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Home
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: Notifications
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Configuring screen: OfferDetail
 LOG  [Navigation] Can go back: true
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [BackButton] Rendering back button
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": null, "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-04T12:30:32.203Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": null, "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:30:32.204Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": null, "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": false, "offerOwnerId": null, "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:30:32.208Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": null, "shouldShowAbusiveButton": false, "timestamp": "2025-06-04T12:30:32.219Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.220Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.220Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.220Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.220Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.220Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.220Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.221Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.221Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.221Z"}
 LOG  [useAuthUser] Setting up auth state listener {"currentUser": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "1731681044939", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "1749039583957", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "hasAuth": true, "hasOnAuthStateChanged": true}
 LOG  [OfferDetail][MOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "MOUNT", "mountCount": 0, "params": {"offerId": "IxPWHv5aJN5qViIRlCho", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "57F81UMKDYW9nl6hU9kI", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "timestamp": "2025-06-04T12:30:32.225Z"}
 LOG  [Messages Subscription] Setting up subscription for offerId: IxPWHv5aJN5qViIRlCho
 LOG  === OfferDetail Screen Mounted ===
 LOG  Route params: {"offerId": "IxPWHv5aJN5qViIRlCho", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "57F81UMKDYW9nl6hU9kI", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  Setting owner IDs from route params: {"offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [OfferDetail][MOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "MOUNT", "mountCount": 1, "params": {"offerId": "IxPWHv5aJN5qViIRlCho", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "57F81UMKDYW9nl6hU9kI", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "timestamp": "2025-06-04T12:30:32.226Z"}
 LOG  Authenticated user: ybEyGDxbvleCEXdTvrA2CgVTJ1y2
 LOG  [OfferDetail] Initializing with params: {"hasInitialOffer": false, "hasInitialPosting": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.226Z"}
 LOG  [OfferDetail] Setting postingOwnerId from route params: {"postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
 LOG  [OfferDetail] Setting offerOwnerId from route params: {"offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  OfferDetail refresh button states: {"showOfferRefresh": false, "showOfferRefreshExplicit": false, "showPostingRefresh": false, "showPostingRefreshExplicit": false, "timestamp": "2025-06-04T12:30:32.227Z"}
 LOG  [OfferDetail][NOTIFICATION_LISTENER_SETUP] {"offerId": "IxPWHv5aJN5qViIRlCho", "postingId": undefined, "timestamp": "2025-06-04T12:30:32.227Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_LISTENER_SETUP] {"offerId": "IxPWHv5aJN5qViIRlCho", "postingId": undefined, "timestamp": "2025-06-04T12:30:32.227Z", "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-04T12:30:32.229Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:30:32.229Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:30:32.234Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.242Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.242Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.242Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.243Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.243Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-04T12:30:32.243Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.243Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.243Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.243Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.243Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.244Z"}
 LOG  [useAuthUser] Auth state changed {"user": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "1731681044939", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "1749039583957", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}}
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 0, "timestamp": "2025-06-04T12:30:32.259Z"}
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true
 LOG  Region changing: {"latitude": 37.813802134300765, "latitudeDelta": 0.019765574962200105, "longitude": -122.42102023001881, "longitudeDelta": 0.01609314918363225}
 LOG  [Messages Subscription] Received snapshot update: {"empty": false, "metadata": {"fromCache": false, "hasPendingWrites": false}, "size": 1}
 LOG  [Messages Subscription] Processing discussion: {"id": "mzxZDg3AlMCbjwAehE9X", "messageCount": 1}
 LOG  [Messages Subscription] Raw messages: {"count": 1, "messageKeys": ["senderId", "timestamp", "type", "read", "text"], "sampleMessage": {"read": true, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749040190}, "type": "system"}}
 LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["senderId", "timestamp", "type", "read", "text"], "timestampType": "object"}
 LOG  [Messages Subscription] Validation summary: {"firstValidMessage": {"id": undefined, "senderId": "system", "timestamp": {"nanoseconds": 0, "seconds": 1749040190}}, "invalidMessages": 0, "totalMessages": 1, "validMessages": 1}
 LOG  [Messages Subscription] Processing messages: {"hasLegacyMessages": true, "messageTypes": [{"hasId": false, "hasTimestamp": true, "timestampType": "object"}], "newCount": 1, "prevCount": 0}
 LOG  [Messages Subscription] Updated messages state: {"firstMessage": {"id": undefined, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749040190}}, "hasLegacyMessages": true, "lastMessage": {"id": undefined, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749040190}}, "newCount": 1, "previousCount": 0}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-04T12:30:32.438Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:30:32.439Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:30:32.444Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.457Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.457Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.458Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.458Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.458Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-04T12:30:32.458Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.458Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.459Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.459Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.459Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.459Z"}
 LOG  Marking messages as read for authorized user: {"isOfferOwner": true, "isPostingOwner": false, "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  Initial load, scrolling to end: 1
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-04T12:30:32.462Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:30:32.463Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:30:32.467Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.478Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.478Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.479Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.479Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.479Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-04T12:30:32.479Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.479Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.479Z"}
 LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.480Z"}
 LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.480Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.480Z"}
 LOG  FlatList layout complete
 LOG  Skipping load more: {"hasLastDoc": false, "hasMoreMessages": true, "lastDocId": undefined, "loadingMore": false}
 LOG  [OfferCache][SET] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.492Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "IxPWHv5aJN5qViIRlCho", "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:30:32.492Z"}
 LOG  [OfferCache][SET] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.493Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "IxPWHv5aJN5qViIRlCho", "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:30:32.493Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Deleted", "postingTitle": "kous post to be deleted", "timestamp": "2025-06-04T12:30:32.495Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:30:32.495Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "414", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:30:32.499Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.507Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.507Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.507Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.507Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.507Z"}
 LOG  [OfferDetail][NOTIFICATION_LISTENER_CLEANUP] {"offerId": "IxPWHv5aJN5qViIRlCho", "postingId": undefined, "timestamp": "2025-06-04T12:30:32.508Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-04T12:30:32.508Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.508Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.509Z"}
 LOG  [OfferCache][GET] {"age": 16, "found": true, "isExpired": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.509Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.509Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.509Z"}
 LOG  [OfferDetail][NOTIFICATION_LISTENER_SETUP] {"offerId": "IxPWHv5aJN5qViIRlCho", "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:30:32.509Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_LISTENER_SETUP] {"offerId": "IxPWHv5aJN5qViIRlCho", "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:30:32.509Z", "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Deleted", "postingTitle": "kous post to be deleted", "timestamp": "2025-06-04T12:30:32.511Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:30:32.511Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "414", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:30:32.515Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.522Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.523Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.523Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.523Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.540Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-04T12:30:32.541Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.541Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.541Z"}
 LOG  [OfferCache][GET] {"age": 48, "found": true, "isExpired": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.541Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.541Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.542Z"}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Deleted", "postingTitle": "kous post to be deleted", "timestamp": "2025-06-04T12:30:32.544Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:30:32.544Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "414", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:30:32.548Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.557Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.557Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.557Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.557Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.557Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-04T12:30:32.558Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.558Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.558Z"}
 LOG  [OfferCache][GET] {"age": 65, "found": true, "isExpired": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.558Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.558Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.559Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hots offer", "hookPrice": 414, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "414", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:30:32.560Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hots offer", "offerId": "IxPWHv5aJN5qViIRlCho", "price": 414, "timestamp": "2025-06-04T12:30:32.562Z"}
 LOG  [OfferCache][SET] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.564Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "IxPWHv5aJN5qViIRlCho", "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:30:32.564Z"}
 LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 0, "timestamp": "2025-06-04T12:30:32.566Z"}
 LOG  FlatList layout complete
 LOG  Skipping load more: {"hasLastDoc": false, "hasMoreMessages": true, "lastDocId": undefined, "loadingMore": false}
 LOG  Debounced region changed: {"latitude": 37.813802134300765, "latitudeDelta": 0.019765574962200105, "longitude": -122.42102023001881, "longitudeDelta": 0.01609314918363225}
 LOG  Fetching postings for new region
 LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.813802134300765, "latitudeDelta": 0.019765574962200105, "longitude": -122.42102023001881, "longitudeDelta": 0.01609314918363225}
 LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "test", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Deleted", "postingTitle": "kous post to be deleted", "timestamp": "2025-06-04T12:30:32.668Z", "usingLocalData": false}
 LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:30:32.669Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "414", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:30:32.674Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.683Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.684Z"}
 LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.684Z"}
 LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.684Z"}
 LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.684Z"}
 LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-04T12:30:32.684Z"}
 LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.685Z"}
 LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.685Z"}
 LOG  [OfferCache][GET] {"age": 121, "found": true, "isExpired": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.685Z"}
 LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.685Z"}
 LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.685Z"}
 LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hots offer", "hookPrice": 414, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hots offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "414", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
 LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T12:30:32.687Z"}
 LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hots offer", "offerId": "IxPWHv5aJN5qViIRlCho", "price": 414, "timestamp": "2025-06-04T12:30:32.690Z"}
 LOG  [OfferCache][SET] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.735Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "IxPWHv5aJN5qViIRlCho", "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:30:32.735Z"}
 LOG  [OfferCache][SET] {"offerId": "IxPWHv5aJN5qViIRlCho", "timestamp": "2025-06-04T12:30:32.738Z"}
 LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "IxPWHv5aJN5qViIRlCho", "postingId": "57F81UMKDYW9nl6hU9kI", "timestamp": "2025-06-04T12:30:32.738Z"}
 LOG  [fetchPostingsBySearch] Query results: 1
 LOG  [useControlledPostings] Fetched postings: 1
 LOG  [Navigation] Rendering headerLeft for: Notifications
 LOG  [Navigation] Can go back status: true
 LOG  [Navigation] Rendering headerLeft for: OfferDetail
 LOG  [Navigation] Can go back status: true

