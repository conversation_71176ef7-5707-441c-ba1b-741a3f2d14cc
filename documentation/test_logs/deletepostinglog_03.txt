Setup Two Simulators:
Simulator 1: Posting owner 
Simulator 2: Offer owner 

Create Test Data:
On Simulator 1: Create a new posting with a distinctive title -done




LOG  Posting added with ID: v8sq7oTdtyuHwQo42gxY
LOG  [Navigation] Configuring screen: LoginScreen
LOG  [Navigation] Can go back: true
LOG  [Navigation] Configuring screen: Home
LOG  [Navigation] Can go back: true
LOG  [Navigation] Rendering headerLeft for: CreatePosting
LOG  [Navigation] Can go back status: true
LOG  [Navigation] Configuring screen: LoginScreen
LOG  [Navigation] Can go back: true
LOG  [Navigation] Configuring screen: Home
LOG  [Navigation] Can go back: true
LOG  [Navigation] Rendering headerLeft for: CreatePosting
LOG  [Navigation] Can go back status: true
LOG  Region changing: {"latitude": 37.800689875517435, "latitudeDelta": 0.027534294264697223, "longitude": -122.42386199183186, "longitudeDelta": 0.017251852310920412}
LOG  Debounced region changed: {"latitude": 37.800689875517435, "latitudeDelta": 0.027534294264697223, "longitude": -122.42386199183186, "longitudeDelta": 0.017251852310920412}
LOG  Fetching postings for new region
LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.800689875517435, "latitudeDelta": 0.027534294264697223, "longitude": -122.42386199183186, "longitudeDelta": 0.017251852310920412}
LOG  [fetchPostingsBySearch] Query results: 10
LOG  [useControlledPostings] Fetched postings: 10
LOG  [useAuthUser] Cleaning up auth state listener


On Simulator 2: Submit an offer on that posting -done




LOG  [MakeOffer][Verify] Checking for active listeners: {"stats": {"activeCleanups": 0, "activeScreens": Set {}, "byCollection": {"offers": 0, "postings": 0}, "lastCleanup": null, "timestamp": "2025-06-04T10:39:00.599Z", "totalListeners": 0}, "timestamp": "2025-06-04T10:39:00.599Z"}
LOG  [MakeOffer][Verify] Checking for active listeners: {"stats": {"activeCleanups": 0, "activeScreens": Set {}, "byCollection": {"offers": 0, "postings": 0}, "lastCleanup": null, "timestamp": "2025-06-04T10:39:05.600Z", "totalListeners": 0}, "timestamp": "2025-06-04T10:39:05.600Z"}




LOG  [MakeOffer] Submitting offer: {"description": "hotmails offer", "postingId": "v8sq7oTdtyuHwQo42gxY", "price": "313"}
LOG  [MakeOffer] Creating offer with data: {"postingId": "v8sq7oTdtyuHwQo42gxY", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "price": 313}
LOG  [addOfferAndDiscussion] Starting with data: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "v8sq7oTdtyuHwQo42gxY", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "price": 313, "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
LOG  [addOfferAndDiscussion] Offer created: {"offerId": "6JYdQTAGUkApzGNWb84b"}
LOG  [addOfferAndDiscussion] Discussion created: {"discussionId": "M07JYIrF4oH6y0TYZaO7"}
LOG  [addOfferAndDiscussion] Creating notification: {"body": "You received a new offer of $313", "createdAt": {"_methodName": "serverTimestamp"}, "offerId": "6JYdQTAGUkApzGNWb84b", "postingId": "v8sq7oTdtyuHwQo42gxY", "read": false, "recipientId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "senderId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "title": "New Offer", "type": "NEW_OFFER"}
LOG  [addOfferAndDiscussion] Notification created successfully
LOG  [addOfferAndDiscussion] Updated user favorites
LOG  [MakeOffer] Offer created successfully: {"offerId": "6JYdQTAGUkApzGNWb84b"}
LOG  [Navigation] Configuring screen: LoginScreen
LOG  [Navigation] Can go back: true
LOG  [Navigation] Configuring screen: Home
LOG  [Navigation] Can go back: true
LOG  [Navigation] Configuring screen: PostingDetail
LOG  [Navigation] Can go back: true
LOG  [Navigation] Rendering headerLeft for: PostingDetail
LOG  [Navigation] Can go back status: true
LOG  [Navigation] Rendering headerLeft for: MakeOffer
LOG  [Navigation] Can go back status: true
LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T10:38:52.136Z"}, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.459Z"}
LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T10:39:09.459Z"}
LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.460Z", "userOffer": null}
LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_v8sq7oTdtyuHwQo42gxY_1749033532372", "offers_v8sq7oTdtyuHwQo42gxY_1749033532372"]}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033532372"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033532372"}
LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T10:39:09.478Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17342, "timestamp": "2025-06-04T10:39:09.478Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17342, "timestamp": "2025-06-04T10:39:09.478Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549479", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549479", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033549479", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033549479", "totalTime": 17343}
LOG  [usePostingDetails][Focus] Screen focused, setting up subscription
LOG  [useOffers][Focus] Screen focused, setting up subscription
LOG  [useFavorites][Focus] Screen focused, fetching favorite status
LOG  [PostingDetail][Focus] Screen focused
LOG  [PostingDetail][Init] Cleaning up existing subscriptions before setup
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549479"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549479"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17344, "timestamp": "2025-06-04T10:39:09.480Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549480", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549480", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033549480", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033549480", "totalTime": 17344}
LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T10:38:52.136Z"}, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.482Z"}
LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T10:39:09.483Z"}
LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": false, "isOwner": false, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.483Z", "userOffer": null}
LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_v8sq7oTdtyuHwQo42gxY_1749033549480", "offers_v8sq7oTdtyuHwQo42gxY_1749033549480"]}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549480"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549480"}
LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T10:39:09.490Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17354, "timestamp": "2025-06-04T10:39:09.490Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17354, "timestamp": "2025-06-04T10:39:09.490Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549490", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549490", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033549490", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033549490", "totalTime": 17355}
LOG  [usePostingDetails][Setup] Creating references
LOG  [usePostingDetails][Setup] Pre-snapshot listener setup: {"postingId": "v8sq7oTdtyuHwQo42gxY", "ref": "postings/v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.493Z"}
LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T10:38:52.136Z"}, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.494Z"}
LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": false, "postingDetails": {"createdAt": [Timestamp], "description": "delete me", "id": "v8sq7oTdtyuHwQo42gxY", "latitude": 37.808224804895815, "longitude": -122.41710995469978, "postingStatus": "Active", "searchTerms": [Array], "title": "posting to be deleted", "titleLower": "posting to be deleted", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 17358, "timestamp": "2025-06-04T10:39:09.494Z", "updateCount": 3}, "type": "FETCH_START"}
LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": true, "timestamp": "2025-06-04T10:39:09.497Z"}
LOG  [ListenerStateManager] Starting transition for postings_1749033549498_1749033549499: inactive -> initializing
LOG  [ListenerRegistry][postings] Registered: {"collection": "postings", "id": "1749033549498", "registryId": "postings_1749033549498_1749033549499", "state": "active", "timestamp": "2025-06-04T10:39:09.499Z"}
LOG  [ListenerStateManager] Completing transition for postings_1749033549498_1749033549499
LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_v8sq7oTdtyuHwQo42gxY", "listenerId": "postings_1749033549498_1749033549499", "timestamp": "2025-06-04T10:39:09.499Z"}
LOG  [ListenerRegistry][postings] Created: {"componentId": "PostingDetail_v8sq7oTdtyuHwQo42gxY", "listenerId": "postings_1749033549498", "timestamp": "2025-06-04T10:39:09.499Z"}
LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_v8sq7oTdtyuHwQo42gxY_1749033549490", "offers_v8sq7oTdtyuHwQo42gxY_1749033549490"]}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549490"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549490"}
LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T10:39:09.500Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17364, "timestamp": "2025-06-04T10:39:09.500Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17364, "timestamp": "2025-06-04T10:39:09.500Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549500", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549500", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033549500", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033549500", "totalTime": 17364}
LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hasDetails": true, "offersCount": 0, "postingLoading": true, "timestamp": "2025-06-04T10:39:09.500Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
LOG  [useOffers][Subscribe] Setting up subscription {"postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.502Z"}
LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T10:38:52.136Z"}, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.503Z"}
LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": true, "timestamp": "2025-06-04T10:39:09.503Z"}
LOG  [ListenerStateManager] Starting transition for offers_1749033549504_1749033549504: inactive -> initializing
LOG  [ListenerRegistry][offers] Registered: {"collection": "offers", "id": "1749033549504", "registryId": "offers_1749033549504_1749033549504", "state": "active", "timestamp": "2025-06-04T10:39:09.504Z"}
LOG  [ListenerStateManager] Completing transition for offers_1749033549504_1749033549504
LOG  [ListenerRegistry][Component] Registered listener with component: {"componentId": "PostingDetail_v8sq7oTdtyuHwQo42gxY", "listenerId": "offers_1749033549504_1749033549504", "timestamp": "2025-06-04T10:39:09.504Z"}
LOG  [ListenerRegistry][offers] Created: {"componentId": "PostingDetail_v8sq7oTdtyuHwQo42gxY", "listenerId": "offers_1749033549504", "timestamp": "2025-06-04T10:39:09.505Z"}
LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_v8sq7oTdtyuHwQo42gxY_1749033549500", "offers_v8sq7oTdtyuHwQo42gxY_1749033549500"]}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549500"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549500"}
LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T10:39:09.506Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17370, "timestamp": "2025-06-04T10:39:09.506Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17370, "timestamp": "2025-06-04T10:39:09.506Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549507", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549507", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033549507", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033549507", "totalTime": 17371}
LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T10:38:52.136Z"}, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.586Z"}
LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": true, "timestamp": "2025-06-04T10:39:09.586Z"}
LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_v8sq7oTdtyuHwQo42gxY_1749033549507", "offers_v8sq7oTdtyuHwQo42gxY_1749033549507"]}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549507"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549507"}
LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T10:39:09.588Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17453, "timestamp": "2025-06-04T10:39:09.589Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17453, "timestamp": "2025-06-04T10:39:09.589Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549589", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549589", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033549589", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033549589", "totalTime": 17453}
LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T10:38:52.136Z"}, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.591Z"}
LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": true, "timestamp": "2025-06-04T10:39:09.591Z"}
LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_v8sq7oTdtyuHwQo42gxY_1749033549589", "offers_v8sq7oTdtyuHwQo42gxY_1749033549589"]}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549589"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549589"}
LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T10:39:09.592Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17457, "timestamp": "2025-06-04T10:39:09.593Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17457, "timestamp": "2025-06-04T10:39:09.593Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549593", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549593", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033549593", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033549593", "totalTime": 17457}
LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T10:38:52.136Z"}, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.636Z"}
LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": true, "postingDetails": {"createdAt": [Timestamp], "description": "delete me", "id": "v8sq7oTdtyuHwQo42gxY", "latitude": 37.808224804895815, "longitude": -122.41710995469978, "postingStatus": "Active", "searchTerms": [Array], "title": "posting to be deleted", "titleLower": "posting to be deleted", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 17501, "timestamp": "2025-06-04T10:39:09.637Z", "updateCount": 4}, "type": "UPDATE_POSTING"}
LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T10:39:09.638Z"}
LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.638Z", "userOffer": null}
LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_v8sq7oTdtyuHwQo42gxY_1749033549593", "offers_v8sq7oTdtyuHwQo42gxY_1749033549593"]}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549593"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549593"}
LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T10:39:09.649Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17514, "timestamp": "2025-06-04T10:39:09.650Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17515, "timestamp": "2025-06-04T10:39:09.651Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549651", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549651", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033549651", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033549651", "totalTime": 17515}
LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hasDetails": true, "offersCount": 0, "postingLoading": false, "timestamp": "2025-06-04T10:39:09.652Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isOwnerCheck": false, "offersCount": 0, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.652Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
LOG  [PostingDetail][StateSync] Processing offers: {"count": 0, "timestamp": "2025-06-04T10:39:09.652Z", "userOffers": 0}
LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": false, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.652Z"}
LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "v8sq7oTdtyuHwQo42gxY", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749033532366, "offersCount": 0, "postingDetailsId": "v8sq7oTdtyuHwQo42gxY", "userOffer": null}, "timestamp": "2025-06-04T10:39:09.652Z"}
LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": true, "isOwner": false, "timestamp": "2025-06-04T10:39:09.653Z"}
LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T10:38:52.136Z"}, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.653Z"}
LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T10:39:09.654Z"}
LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.654Z", "userOffer": null}
LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_v8sq7oTdtyuHwQo42gxY_1749033549651", "offers_v8sq7oTdtyuHwQo42gxY_1749033549651"]}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549651"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549651"}
LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T10:39:09.660Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17524, "timestamp": "2025-06-04T10:39:09.660Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17525, "timestamp": "2025-06-04T10:39:09.661Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549661", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549661", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033549661", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033549661", "totalTime": 17525}
LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "v8sq7oTdtyuHwQo42gxY", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749033549653, "offersCount": 0, "postingDetailsId": "v8sq7oTdtyuHwQo42gxY", "userOffer": null}, "timestamp": "2025-06-04T10:39:09.661Z"}
LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "performance": {"timeSinceLastUpdate": 8}, "props": {"postingId": "v8sq7oTdtyuHwQo42gxY", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749033549653, "offersCount": 0, "postingDetailsId": "v8sq7oTdtyuHwQo42gxY", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T10:38:52.136Z", "subscriptionCount": 0, "totalTime": 17525}, "types": []}, "timestamp": "2025-06-04T10:39:09.661Z"}
LOG  [useOffers][Update] {"changes": [{"id": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:39:09.663Z", "type": "added"}], "count": 1, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.663Z"}
LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T10:38:52.136Z"}, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.664Z"}
LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T10:39:09.664Z"}
LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.664Z", "userOffer": null}
LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_v8sq7oTdtyuHwQo42gxY_1749033549661", "offers_v8sq7oTdtyuHwQo42gxY_1749033549661"]}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549661"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549661"}
LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T10:39:09.671Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17535, "timestamp": "2025-06-04T10:39:09.671Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17535, "timestamp": "2025-06-04T10:39:09.671Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549672", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549672", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033549672", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033549672", "totalTime": 17536}
LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T10:39:09.672Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isOwnerCheck": false, "offersCount": 1, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.672Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T10:39:09.672Z", "userOffers": 1}
LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": true, "isOwner": false, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.672Z"}
LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T10:38:52.136Z"}, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.674Z"}
LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T10:39:09.676Z"}
LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isFavorite": true, "isOwner": false, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:09.676Z", "userOffer": {"id": "6JYdQTAGUkApzGNWb84b", "status": "pending"}}
LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_v8sq7oTdtyuHwQo42gxY_1749033549672", "offers_v8sq7oTdtyuHwQo42gxY_1749033549672"]}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549672"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549672"}
LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T10:39:09.682Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17546, "timestamp": "2025-06-04T10:39:09.682Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 17547, "timestamp": "2025-06-04T10:39:09.683Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033549683", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033549683", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033549683", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033549683", "totalTime": 17547}
LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "v8sq7oTdtyuHwQo42gxY", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749033549674, "offersCount": 1, "postingDetailsId": "v8sq7oTdtyuHwQo42gxY", "userOffer": {"id": "6JYdQTAGUkApzGNWb84b", "status": "pending"}}, "timestamp": "2025-06-04T10:39:09.683Z"}
LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "performance": {"timeSinceLastUpdate": 9}, "props": {"postingId": "v8sq7oTdtyuHwQo42gxY", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDescriptionExpanded": false, "isOwner": false, "lastUpdate": 1749033549674, "offersCount": 1, "postingDetailsId": "v8sq7oTdtyuHwQo42gxY", "userOffer": {"id": "6JYdQTAGUkApzGNWb84b", "status": "pending"}}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T10:38:52.136Z", "subscriptionCount": 0, "totalTime": 17547}, "types": []}, "timestamp": "2025-06-04T10:39:09.683Z"}
LOG  [Navigation] Rendering headerLeft for: PostingDetail
LOG  [Navigation] Can go back status: true
LOG  [Navigation] Rendering headerLeft for: PostingDetail
LOG  [Navigation] Can go back status: true
LOG  Region changing: {"latitude": 37.80422654984636, "latitudeDelta": 0.0219382255231082, "longitude": -122.4178509483721, "longitudeDelta": 0.01609314918366067}
LOG  Debounced region changed: {"latitude": 37.80422654984636, "latitudeDelta": 0.0219382255231082, "longitude": -122.4178509483721, "longitudeDelta": 0.01609314918366067}
LOG  Fetching postings for new region
LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.80422654984636, "latitudeDelta": 0.0219382255231082, "longitude": -122.4178509483721, "longitudeDelta": 0.01609314918366067}
LOG  [fetchPostingsBySearch] Query results: 5
LOG  [useControlledPostings] Fetched postings: 5





Execute Test:
On Simulator 1: Delete the posting and observe logs





LOG  === Starting Posting Deletion Flow ===
LOG  PostingId: v8sq7oTdtyuHwQo42gxY
LOG  [deletePosting] Starting deletion process {"postingId": "v8sq7oTdtyuHwQo42gxY", "stack": "Error
   at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:193399:25)
   at next (native)
   at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5917:19)
   at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5931:29)
   at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5936:14)
   at tryCallTwo (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:61:9)
   at doResolve (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:216:25)
   at Promise (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:82:14)
   at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5928:25)
   at apply (native)
   at deletePosting (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:193636:26)
   at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:261386:56)
   at next (native)
   at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5917:19)
   at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5931:29)
   at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5936:14)
   at tryCallTwo (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:61:9)
   at doResolve (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:216:25)
   at Promise (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:82:14)
   at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5928:25)
   at apply (native)
   at onPress (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:261401:34)
   at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:42025:21)
   at apply (native)
   at __invokeCallback (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3648:23)
   at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3343:34)
   at __guard (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3551:15)
   at invokeCallbackAndReturnFlushedQueue (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3342:21)", "timestamp": "2025-06-04T10:39:50.888Z"}
LOG  [deletePosting] Fetching active offers...
LOG  [deletePosting] Found offers: {"count": 1, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.035Z"}
LOG  [deletePosting] Marking posting as deleted: {"postingId": "v8sq7oTdtyuHwQo42gxY", "postingTitle": "posting to be deleted", "timestamp": "2025-06-04T10:39:51.038Z"}
LOG  [deletePosting] Processing offer: {"offerId": "6JYdQTAGUkApzGNWb84b", "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.040Z"}
LOG  [deletePosting] Added system message to discussion: {"discussionId": "M07JYIrF4oH6y0TYZaO7", "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:39:51.196Z"}
LOG  [deletePosting] Creating notification for offer owner: {"offerId": "6JYdQTAGUkApzGNWb84b", "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "timestamp": "2025-06-04T10:39:51.196Z", "type": "SYSTEM_WITHDRAWAL"}
LOG  [deletePosting] Committing batch updates...
LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T10:39:40.624Z"}, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.202Z"}
LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": false, "postingDetails": {"createdAt": [Timestamp], "description": "delete me", "id": "v8sq7oTdtyuHwQo42gxY", "latitude": 37.808224804895815, "longitude": -122.41710995469978, "postingStatus": "Active", "searchTerms": [Array], "title": "posting to be deleted", "titleLower": "posting to be deleted", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 10578, "timestamp": "2025-06-04T10:39:51.202Z", "updateCount": 3}, "type": "UPDATE_POSTING"}
LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T10:39:51.204Z"}
LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.204Z", "userOffer": null}
LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_v8sq7oTdtyuHwQo42gxY_1749033580850", "offers_v8sq7oTdtyuHwQo42gxY_1749033580850"]}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033580850"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033580850"}
LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T10:39:51.212Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 10588, "timestamp": "2025-06-04T10:39:51.212Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 10588, "timestamp": "2025-06-04T10:39:51.212Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033591212", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033591212", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033591212", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033591212", "totalTime": 10588}
LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T10:39:51.212Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.213Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T10:39:51.214Z", "userOffers": 0}
LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.214Z"}
LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "v8sq7oTdtyuHwQo42gxY", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749033580842, "offersCount": 1, "postingDetailsId": "v8sq7oTdtyuHwQo42gxY", "userOffer": null}, "timestamp": "2025-06-04T10:39:51.215Z"}
LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": true, "isOwner": true, "timestamp": "2025-06-04T10:39:51.215Z"}
LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T10:39:40.624Z"}, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.216Z"}
LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T10:39:51.216Z"}
LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.217Z", "userOffer": null}
LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_v8sq7oTdtyuHwQo42gxY_1749033591212", "offers_v8sq7oTdtyuHwQo42gxY_1749033591212"]}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033591212"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033591212"}
LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T10:39:51.223Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 10599, "timestamp": "2025-06-04T10:39:51.223Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 10599, "timestamp": "2025-06-04T10:39:51.223Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033591223", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033591223", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033591223", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033591223", "totalTime": 10600}
LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "v8sq7oTdtyuHwQo42gxY", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749033591216, "offersCount": 1, "postingDetailsId": "v8sq7oTdtyuHwQo42gxY", "userOffer": null}, "timestamp": "2025-06-04T10:39:51.224Z"}
LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 8}, "props": {"postingId": "v8sq7oTdtyuHwQo42gxY", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749033591216, "offersCount": 1, "postingDetailsId": "v8sq7oTdtyuHwQo42gxY", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T10:39:40.624Z", "subscriptionCount": 0, "totalTime": 10600}, "types": []}, "timestamp": "2025-06-04T10:39:51.224Z"}
LOG  [useOffers][Update] {"changes": [{"id": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:39:51.225Z", "type": "modified"}], "count": 1, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.225Z"}
LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T10:39:40.624Z"}, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.226Z"}
LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T10:39:51.227Z"}
LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.227Z", "userOffer": null}
LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_v8sq7oTdtyuHwQo42gxY_1749033591223", "offers_v8sq7oTdtyuHwQo42gxY_1749033591223"]}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033591223"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033591223"}
LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T10:39:51.234Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 10610, "timestamp": "2025-06-04T10:39:51.234Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 10610, "timestamp": "2025-06-04T10:39:51.234Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033591235", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033591235", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033591235", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033591235", "totalTime": 10611}
LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T10:39:51.235Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.235Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T10:39:51.235Z", "userOffers": 0}
LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.235Z"}
LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T10:39:40.624Z"}, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.236Z"}
LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T10:39:51.236Z"}
LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.237Z", "userOffer": null}
LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_v8sq7oTdtyuHwQo42gxY_1749033591235", "offers_v8sq7oTdtyuHwQo42gxY_1749033591235"]}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033591235"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033591235"}
LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T10:39:51.242Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 10618, "timestamp": "2025-06-04T10:39:51.242Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 10618, "timestamp": "2025-06-04T10:39:51.242Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033591242", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033591242", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033591242", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033591242", "totalTime": 10618}
LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "v8sq7oTdtyuHwQo42gxY", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749033591236, "offersCount": 1, "postingDetailsId": "v8sq7oTdtyuHwQo42gxY", "userOffer": null}, "timestamp": "2025-06-04T10:39:51.243Z"}
LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 7}, "props": {"postingId": "v8sq7oTdtyuHwQo42gxY", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749033591236, "offersCount": 1, "postingDetailsId": "v8sq7oTdtyuHwQo42gxY", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T10:39:40.624Z", "subscriptionCount": 0, "totalTime": 10619}, "types": []}, "timestamp": "2025-06-04T10:39:51.243Z"}
LOG  [deletePosting] Notification document created: {"notificationId": "eyL9iO5rWhV4cbaGhbki", "offerId": "6JYdQTAGUkApzGNWb84b", "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "timestamp": "2025-06-04T10:39:51.303Z"}
LOG  [deletePosting] Sending notifications to offer owners... {"notificationCount": 1, "timestamp": "2025-06-04T10:39:51.387Z"}
LOG  [deletePosting] Notification results: {"fulfilled": 1, "rejected": 0, "timestamp": "2025-06-04T10:39:51.388Z", "total": 1}
LOG  [deletePosting] Notification 1 succeeded: {"notificationId": "eyL9iO5rWhV4cbaGhbki", "timestamp": "2025-06-04T10:39:51.389Z"}
LOG  [deletePosting] Process completed successfully: {"notificationsSent": 1, "offersProcessed": 1, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.389Z"}
LOG  Posting deleted successfully
LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T10:39:40.624Z"}, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.398Z"}
LOG  [usePostingDetails][Reducer] Processing action: {"currentState": {"error": null, "loading": false, "postingDetails": {"createdAt": [Timestamp], "deletedAt": null, "description": "delete me", "id": "v8sq7oTdtyuHwQo42gxY", "latitude": 37.808224804895815, "longitude": -122.41710995469978, "postingStatus": "Deleted", "searchTerms": [Array], "title": "posting to be deleted", "titleLower": "posting to be deleted", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}}, "metrics": {"timeSinceStart": 10774, "timestamp": "2025-06-04T10:39:51.398Z", "updateCount": 4}, "type": "UPDATE_POSTING"}
LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T10:39:51.400Z"}
LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.401Z", "userOffer": null}
LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_v8sq7oTdtyuHwQo42gxY_1749033591242", "offers_v8sq7oTdtyuHwQo42gxY_1749033591242"]}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033591242"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033591242"}
LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T10:39:51.410Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 10786, "timestamp": "2025-06-04T10:39:51.410Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 10787, "timestamp": "2025-06-04T10:39:51.411Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033591411", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033591411", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033591411", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033591411", "totalTime": 10787}
LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T10:39:51.412Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.412Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T10:39:51.412Z", "userOffers": 0}
LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.412Z"}
LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "v8sq7oTdtyuHwQo42gxY", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749033591236, "offersCount": 1, "postingDetailsId": "v8sq7oTdtyuHwQo42gxY", "userOffer": null}, "timestamp": "2025-06-04T10:39:51.413Z"}
LOG  [PostingDetail] Posting details loaded for edit: {"hasDetails": true, "hasLocation": true, "isOwner": true, "timestamp": "2025-06-04T10:39:51.413Z"}
LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T10:39:40.624Z"}, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.417Z"}
LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T10:39:51.417Z"}
LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.418Z", "userOffer": null}
LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_v8sq7oTdtyuHwQo42gxY_1749033591411", "offers_v8sq7oTdtyuHwQo42gxY_1749033591411"]}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033591411"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033591411"}
LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T10:39:51.425Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 10802, "timestamp": "2025-06-04T10:39:51.426Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 10802, "timestamp": "2025-06-04T10:39:51.426Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033591426", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033591426", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033591426", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033591426", "totalTime": 10802}
LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "v8sq7oTdtyuHwQo42gxY", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749033591417, "offersCount": 1, "postingDetailsId": "v8sq7oTdtyuHwQo42gxY", "userOffer": null}, "timestamp": "2025-06-04T10:39:51.426Z"}
LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 10}, "props": {"postingId": "v8sq7oTdtyuHwQo42gxY", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749033591417, "offersCount": 1, "postingDetailsId": "v8sq7oTdtyuHwQo42gxY", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T10:39:40.624Z", "subscriptionCount": 0, "totalTime": 10803}, "types": []}, "timestamp": "2025-06-04T10:39:51.427Z"}
LOG  [useOffers][Update] {"changes": [{"id": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:39:51.428Z", "type": "modified"}], "count": 1, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.428Z"}
LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T10:39:40.624Z"}, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.430Z"}
LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T10:39:51.430Z"}
LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.430Z", "userOffer": null}
LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_v8sq7oTdtyuHwQo42gxY_1749033591426", "offers_v8sq7oTdtyuHwQo42gxY_1749033591426"]}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033591426"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033591426"}
LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T10:39:51.436Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 10814, "timestamp": "2025-06-04T10:39:51.438Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 10814, "timestamp": "2025-06-04T10:39:51.438Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033591438", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033591438", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033591438", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033591438", "totalTime": 10815}
LOG  [PostingDetail][StateSync] Checking state update conditions: {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "hasDetails": true, "offersCount": 1, "postingLoading": false, "timestamp": "2025-06-04T10:39:51.439Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
LOG  [PostingDetail][StateSync] Updating state with: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isOwnerCheck": true, "offersCount": 1, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.439Z", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
LOG  [PostingDetail][StateSync] Processing offers: {"count": 1, "timestamp": "2025-06-04T10:39:51.439Z", "userOffers": 0}
LOG  [PostingDetail][StateSync] State updated: {"hasUserOffer": false, "isOwner": true, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.439Z"}
LOG  [usePostingDetails][Init] Starting with postingId: {"metrics": {"startTime": "2025-06-04T10:39:40.624Z"}, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.441Z"}
LOG  [PostingDetail] Component state: {"error": null, "hasPostingDetails": true, "loading": false, "timestamp": "2025-06-04T10:39:51.441Z"}
LOG  [PostingDetail][Render] HeaderActions state: {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isFavorite": false, "isOwner": true, "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:39:51.442Z", "userOffer": null}
LOG  [PostingDetail][Cleanup] Starting {"subscriptionCount": 2, "subscriptions": ["posting_v8sq7oTdtyuHwQo42gxY_1749033591438", "offers_v8sq7oTdtyuHwQo42gxY_1749033591438"]}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033591438"}
LOG  [PostingDetail][Listener] Cleaning up: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033591438"}
LOG  [PostingDetail][Cleanup] Complete {"timestamp": "2025-06-04T10:39:51.447Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 10823, "timestamp": "2025-06-04T10:39:51.447Z"}
LOG  [PostingDetail][Init] Starting initialization {"postingId": "v8sq7oTdtyuHwQo42gxY", "timeSinceStart": 10823, "timestamp": "2025-06-04T10:39:51.447Z"}
LOG  [PostingDetail][Listener] Tracking: {"id": "posting_v8sq7oTdtyuHwQo42gxY_1749033591447", "type": "posting_details"}
LOG  [PostingDetail][Listener] Tracking: {"id": "offers_v8sq7oTdtyuHwQo42gxY_1749033591447", "type": "offers_list"}
LOG  [PostingDetail][Init] Subscriptions setup complete {"offersSubId": "offers_v8sq7oTdtyuHwQo42gxY_1749033591447", "postingSubId": "posting_v8sq7oTdtyuHwQo42gxY_1749033591447", "totalTime": 10824}
LOG  [PostingDetail][StateVerification] {"auth": {"currentUser": "JIPkxLOiOrMcavtTItGBID0HMqH3", "expectedOwner": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "posting": {"id": "v8sq7oTdtyuHwQo42gxY", "loaded": true}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749033591441, "offersCount": 1, "postingDetailsId": "v8sq7oTdtyuHwQo42gxY", "userOffer": null}, "timestamp": "2025-06-04T10:39:51.448Z"}
LOG  [PostingDetail][StateDebug] {"auth": {"currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "performance": {"timeSinceLastUpdate": 7}, "props": {"postingId": "v8sq7oTdtyuHwQo42gxY", "userId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "state": {"activeTab": "active", "currentUserId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "isDescriptionExpanded": false, "isOwner": true, "lastUpdate": 1749033591441, "offersCount": 1, "postingDetailsId": "v8sq7oTdtyuHwQo42gxY", "userOffer": null}, "subscriptions": {"metrics": {"lastUpdate": "2025-06-04T10:39:40.624Z", "subscriptionCount": 0, "totalTime": 10824}, "types": []}, "timestamp": "2025-06-04T10:39:51.448Z"}





On simulator 2: offer owner received notification, clicked on the notification bell in home screen and navigated to notifications screen. clicked on the notification and navigated to the offer detail screen. offer owner saw that the offer was withdrawn and related info messages




LOG  [Navigation] Configuring screen: LoginScreen
LOG  [Navigation] Can go back: true
LOG  [Navigation] Configuring screen: Home
LOG  [Navigation] Can go back: true
LOG  [Navigation] Configuring screen: Notifications
LOG  [Navigation] Can go back: true
LOG  [Navigation] Rendering headerLeft for: Notifications
LOG  [Navigation] Can go back status: true
LOG  [BackButton] Rendering back button
LOG  Setting up notifications subscription
LOG  [notificationService] Setting up notification subscription
LOG  Notification snapshot received, count: 1
LOG  Received 1 notifications
LOG  [Navigation] Rendering headerLeft for: Notifications
LOG  [Navigation] Can go back status: true
LOG  [Navigation] Rendering headerLeft for: Notifications
LOG  [Navigation] Can go back status: true
LOG  Notification snapshot received, count: 430
LOG  Received 430 notifications
LOG  [NotificationsScreen][handleNotificationPress] Starting with: {"hasData": true, "notificationId": "eyL9iO5rWhV4cbaGhbki", "offerId": "6JYdQTAGUkApzGNWb84b", "stack": "Error
   at ?anon_0_ (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:272592:29)
   at next (native)
   at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5917:19)
   at _next (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5931:29)
   at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5936:14)
   at tryCallTwo (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:61:9)
   at doResolve (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:216:25)
   at Promise (/Users/<USER>/react-native/sdks/hermes/build_iphonesimulator/lib/InternalBytecode/InternalBytecode.js:82:14)
   at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:5928:25)
   at apply (native)
   at handleNotificationPress (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:272702:27)
   at onPress (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:272737:41)
   at _performTransitionSideEffects (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79291:22)
   at _receiveSignal (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79241:45)
   at onResponderRelease (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79097:34)
   at apply (native)
   at invokeGuardedCallbackProd (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79798:21)
   at apply (native)
   at invokeGuardedCallback (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79974:42)
   at apply (native)
   at invokeGuardedCallbackAndCatchFirstError (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:79988:36)
   at executeDispatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:80065:48)
   at executeDispatchesInOrder (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:80087:26)
   at executeDispatchesAndRelease (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81858:35)
   at executeDispatchesAndReleaseTopLevel (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81865:43)
   at forEach (native)
   at forEachAccumulated (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:80679:22)
   at runEventsInBatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81878:27)
   at runExtractedPluginEventsInBatch (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81988:25)
   at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81959:42)
   at batchedUpdates$1 (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:95400:20)
   at batchedUpdates (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81834:36)
   at _receiveRootNodeIDEvent (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:81958:23)
   at receiveTouches (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:82047:34)
   at apply (native)
   at __callFunction (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3612:36)
   at anonymous (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3334:31)
   at __guard (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3551:15)
   at callFunctionReturnFlushedQueue (http://localhost:8081/index.bundle//&platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.company.satbana:3333:21)", "type": "SYSTEM_WITHDRAWAL"}
LOG  Notification snapshot received, count: 430
LOG  Received 430 notifications
LOG  [NotificationsScreen] Processing SYSTEM_WITHDRAWAL notification: {"notification": {"createdAt": [Object], "id": "eyL9iO5rWhV4cbaGhbki", "offerId": "6JYdQTAGUkApzGNWb84b", "postingId": "v8sq7oTdtyuHwQo42gxY", "read": false, "recipientId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "senderId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "type": "SYSTEM_WITHDRAWAL"}, "timestamp": "2025-06-04T10:40:44.136Z"}
LOG  [NotificationsScreen] Navigating to OfferDetail for withdrawn offer: {"offerId": "6JYdQTAGUkApzGNWb84b", "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:40:44.137Z", "type": "SYSTEM_WITHDRAWAL"}
LOG  [Navigation] Configuring screen: LoginScreen
LOG  [Navigation] Can go back: true
LOG  [Navigation] Configuring screen: Home
LOG  [Navigation] Can go back: true
LOG  [Navigation] Configuring screen: Notifications
LOG  [Navigation] Can go back: true
LOG  [Navigation] Configuring screen: OfferDetail
LOG  [Navigation] Can go back: true
LOG  [Navigation] Rendering headerLeft for: Notifications
LOG  [Navigation] Can go back status: true
LOG  [Navigation] Rendering headerLeft for: OfferDetail
LOG  [Navigation] Can go back status: true
LOG  [BackButton] Rendering back button
LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": null, "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-04T10:40:44.193Z", "usingLocalData": false}
LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": null, "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T10:40:44.194Z"}
LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": null, "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": false, "offerOwnerId": null, "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T10:40:44.198Z"}
LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": null, "shouldShowAbusiveButton": false, "timestamp": "2025-06-04T10:40:44.210Z"}
LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.210Z"}
LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.210Z"}
LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.210Z"}
LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.210Z"}
LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.211Z"}
LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.212Z"}
LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.212Z"}
LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.212Z"}
LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.212Z"}
LOG  [useAuthUser] Setting up auth state listener {"currentUser": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "1731681044939", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "1749033390462", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}, "hasAuth": true, "hasOnAuthStateChanged": true}
LOG  [OfferDetail][MOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "MOUNT", "mountCount": 0, "params": {"offerId": "6JYdQTAGUkApzGNWb84b", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "v8sq7oTdtyuHwQo42gxY", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "timestamp": "2025-06-04T10:40:44.217Z"}
LOG  [Messages Subscription] Setting up subscription for offerId: 6JYdQTAGUkApzGNWb84b
LOG  === OfferDetail Screen Mounted ===
LOG  Route params: {"offerId": "6JYdQTAGUkApzGNWb84b", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "v8sq7oTdtyuHwQo42gxY", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
LOG  Setting owner IDs from route params: {"offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
LOG  [OfferDetail][MOUNT] {"cleanupStarted": {"isCleaningUp": false, "isMounted": true}, "lastAction": "MOUNT", "mountCount": 1, "params": {"offerId": "6JYdQTAGUkApzGNWb84b", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingId": "v8sq7oTdtyuHwQo42gxY", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}, "timestamp": "2025-06-04T10:40:44.218Z"}
LOG  Authenticated user: ybEyGDxbvleCEXdTvrA2CgVTJ1y2
LOG  [OfferDetail] Initializing with params: {"hasInitialOffer": false, "hasInitialPosting": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.218Z"}
LOG  [OfferDetail] Setting postingOwnerId from route params: {"postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3"}
LOG  [OfferDetail] Setting offerOwnerId from route params: {"offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
LOG  OfferDetail refresh button states: {"showOfferRefresh": false, "showOfferRefreshExplicit": false, "showPostingRefresh": false, "showPostingRefreshExplicit": false, "timestamp": "2025-06-04T10:40:44.218Z"}
LOG  [OfferDetail][NOTIFICATION_LISTENER_SETUP] {"offerId": "6JYdQTAGUkApzGNWb84b", "postingId": undefined, "timestamp": "2025-06-04T10:40:44.220Z"}
LOG  [OfferDetail][NOTIFICATION_DB_LISTENER_SETUP] {"offerId": "6JYdQTAGUkApzGNWb84b", "postingId": undefined, "timestamp": "2025-06-04T10:40:44.220Z", "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-04T10:40:44.222Z", "usingLocalData": false}
LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T10:40:44.222Z"}
LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T10:40:44.226Z"}
LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.234Z"}
LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.234Z"}
LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.234Z"}
LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.236Z"}
LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.236Z"}
LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-04T10:40:44.236Z"}
LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.236Z"}
LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.236Z"}
LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.236Z"}
LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.237Z"}
LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.237Z"}
LOG  [useAuthUser] Auth state changed {"user": {"_redirectEventId": undefined, "apiKey": "AIzaSyBOHewmZjlRWGu1jeEI-uBSkAn56xLMlec", "appName": "[DEFAULT]", "createdAt": "1731681044939", "displayName": undefined, "email": "<EMAIL>", "emailVerified": false, "isAnonymous": false, "lastLoginAt": "1749033390462", "phoneNumber": undefined, "photoURL": undefined, "providerData": [Array], "stsTokenManager": [Object], "tenantId": undefined, "uid": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}}
LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 0, "timestamp": "2025-06-04T10:40:44.252Z"}
LOG  [Navigation] Rendering headerLeft for: OfferDetail
LOG  [Navigation] Can go back status: true
LOG  [Navigation] Rendering headerLeft for: Notifications
LOG  [Navigation] Can go back status: true
LOG  [Navigation] Rendering headerLeft for: OfferDetail
LOG  [Navigation] Can go back status: true
LOG  Region changing: {"latitude": 37.804226550029256, "latitudeDelta": 0.019768138287084014, "longitude": -122.4178509483721, "longitudeDelta": 0.01609314918366067}
LOG  [Messages Subscription] Received snapshot update: {"empty": false, "metadata": {"fromCache": false, "hasPendingWrites": false}, "size": 1}
LOG  [Messages Subscription] Processing discussion: {"id": "M07JYIrF4oH6y0TYZaO7", "messageCount": 1}
LOG  [Messages Subscription] Raw messages: {"count": 1, "messageKeys": ["senderId", "text", "read", "type", "timestamp"], "sampleMessage": {"read": true, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749033591}, "type": "system"}}
LOG  [validateMessage] Validating message: {"hasMessage": true, "messageStructure": ["senderId", "text", "read", "type", "timestamp"], "timestampType": "object"}
LOG  [Messages Subscription] Validation summary: {"firstValidMessage": {"id": undefined, "senderId": "system", "timestamp": {"nanoseconds": 0, "seconds": 1749033591}}, "invalidMessages": 0, "totalMessages": 1, "validMessages": 1}
LOG  [Messages Subscription] Processing messages: {"hasLegacyMessages": true, "messageTypes": [{"hasId": false, "hasTimestamp": true, "timestampType": "object"}], "newCount": 1, "prevCount": 0}
LOG  [Messages Subscription] Updated messages state: {"firstMessage": {"id": undefined, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749033591}}, "hasLegacyMessages": true, "lastMessage": {"id": undefined, "senderId": "system", "text": "This offer has been automatically withdrawn because the posting was deleted.", "timestamp": {"nanoseconds": 0, "seconds": 1749033591}}, "newCount": 1, "previousCount": 0}
LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-04T10:40:44.415Z", "usingLocalData": false}
LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T10:40:44.416Z"}
LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": undefined, "usingHookData": false}
LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T10:40:44.421Z"}
LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.434Z"}
LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.434Z"}
LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.434Z"}
LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.435Z"}
LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.436Z"}
LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-04T10:40:44.436Z"}
LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.436Z"}
LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.436Z"}
LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.436Z"}
LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.436Z"}
LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.437Z"}
LOG  Marking messages as read for authorized user: {"isOfferOwner": true, "isPostingOwner": false, "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
LOG  Initial load, scrolling to end: 1
LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Active", "postingTitle": "", "timestamp": "2025-06-04T10:40:44.440Z", "usingLocalData": false}
LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T10:40:44.441Z"}
LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "0", "offerStatus": "active", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T10:40:44.446Z"}
LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.456Z"}
LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.456Z"}
LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.456Z"}
LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.456Z"}
LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.456Z"}
LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": true, "timestamp": "2025-06-04T10:40:44.457Z"}
LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.457Z"}
LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.457Z"}
LOG  [useOfferDetails][FETCHING_DATA] {"fetchCount": 1, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.457Z"}
LOG  [useOfferDetails][FETCH_OFFER_START] {"forceRefresh": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.457Z"}
LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.458Z"}
LOG  FlatList layout complete
LOG  Skipping load more: {"hasLastDoc": false, "hasMoreMessages": true, "lastDocId": undefined, "loadingMore": false}
LOG  [OfferCache][SET] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.476Z"}
LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "6JYdQTAGUkApzGNWb84b", "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:40:44.476Z"}
LOG  [OfferCache][SET] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.476Z"}
LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "6JYdQTAGUkApzGNWb84b", "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:40:44.476Z"}
LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "delete me", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Deleted", "postingTitle": "posting to be deleted", "timestamp": "2025-06-04T10:40:44.479Z", "usingLocalData": false}
LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T10:40:44.479Z"}
LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "313", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T10:40:44.483Z"}
LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.490Z"}
LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.490Z"}
LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.490Z"}
LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.490Z"}
LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.490Z"}
LOG  [OfferDetail][NOTIFICATION_LISTENER_CLEANUP] {"offerId": "6JYdQTAGUkApzGNWb84b", "postingId": undefined, "timestamp": "2025-06-04T10:40:44.491Z"}
LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-04T10:40:44.492Z"}
LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.492Z"}
LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.492Z"}
LOG  [OfferCache][GET] {"age": 16, "found": true, "isExpired": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.492Z"}
LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.493Z"}
LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.494Z"}
LOG  [OfferDetail][NOTIFICATION_LISTENER_SETUP] {"offerId": "6JYdQTAGUkApzGNWb84b", "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:40:44.494Z"}
LOG  [OfferDetail][NOTIFICATION_DB_LISTENER_SETUP] {"offerId": "6JYdQTAGUkApzGNWb84b", "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:40:44.494Z", "userId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2"}
LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": true, "postingDescription": "delete me", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Deleted", "postingTitle": "posting to be deleted", "timestamp": "2025-06-04T10:40:44.496Z", "usingLocalData": false}
LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T10:40:44.496Z"}
LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": true, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "313", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T10:40:44.499Z"}
LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.505Z"}
LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.523Z"}
LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.524Z"}
LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.525Z"}
LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.525Z"}
LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-04T10:40:44.525Z"}
LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.525Z"}
LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.525Z"}
LOG  [OfferCache][GET] {"age": 50, "found": true, "isExpired": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.526Z"}
LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.526Z"}
LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.527Z"}
LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "delete me", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Deleted", "postingTitle": "posting to be deleted", "timestamp": "2025-06-04T10:40:44.529Z", "usingLocalData": false}
LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T10:40:44.530Z"}
LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "313", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T10:40:44.534Z"}
LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.541Z"}
LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.542Z"}
LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.542Z"}
LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.542Z"}
LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.542Z"}
LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-04T10:40:44.542Z"}
LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.542Z"}
LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.543Z"}
LOG  [OfferCache][GET] {"age": 67, "found": true, "isExpired": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.543Z"}
LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.543Z"}
LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.543Z"}
LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offer", "hookPrice": 313, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "313", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T10:40:44.546Z"}
LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offer", "offerId": "6JYdQTAGUkApzGNWb84b", "price": 313, "timestamp": "2025-06-04T10:40:44.548Z"}
LOG  [OfferCache][SET] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.550Z"}
LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "6JYdQTAGUkApzGNWb84b", "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:40:44.550Z"}
LOG  [OfferDetail][NOTIFICATION_DB_UPDATE] {"count": 0, "timestamp": "2025-06-04T10:40:44.552Z"}
LOG  FlatList layout complete
LOG  Skipping load more: {"hasLastDoc": false, "hasMoreMessages": true, "lastDocId": undefined, "loadingMore": false}
LOG  Debounced region changed: {"latitude": 37.804226550029256, "latitudeDelta": 0.019768138287084014, "longitude": -122.4178509483721, "longitudeDelta": 0.01609314918366067}
LOG  Fetching postings for new region
LOG  [useControlledPostings] Fetching postings for region: {"latitude": 37.804226550029256, "latitudeDelta": 0.019768138287084014, "longitude": -122.4178509483721, "longitudeDelta": 0.01609314918366067}
LOG  [PostingDetailsSection] Render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isDetailsExpanded": false, "isLoading": false, "postingDescription": "delete me", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "postingStatus": "Deleted", "postingTitle": "posting to be deleted", "timestamp": "2025-06-04T10:40:44.665Z", "usingLocalData": false}
LOG  [PostingDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserPostingOwner": false, "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T10:40:44.666Z"}
LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": undefined, "hookPrice": undefined, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "313", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": false}
LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T10:40:44.670Z"}
LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.679Z"}
LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.679Z"}
LOG  [OfferDetailsSection][UNMOUNT] Cleaning up {"hasCleanup": true, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.679Z"}
LOG  [useOfferDetails][CLEANUP] {"hasOfferListener": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.679Z"}
LOG  [useOfferDetails][CLEANUP_COMPLETE] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.680Z"}
LOG  [PostingDetailsSection] Visibility check: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "postingOwnerId": "JIPkxLOiOrMcavtTItGBID0HMqH3", "shouldShowAbusiveButton": false, "timestamp": "2025-06-04T10:40:44.680Z"}
LOG  [useOfferDetails][TOGGLING_STATE] {"isToggling": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.680Z"}
LOG  [useOfferDetails][EFFECT_TRIGGERED] {"fetchCount": 0, "hasInitialOffer": false, "isFirstRender": true, "isToggling": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.680Z"}
LOG  [OfferCache][GET] {"age": 130, "found": true, "isExpired": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.680Z"}
LOG  [useOfferDetails][USING_CACHED_DATA_EFFECT] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.681Z"}
LOG  [OfferDetailsSection][HOOK_CALL] {"count": 1, "isExpanded": false, "offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.681Z"}
LOG  OfferDetailsSection render: {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "hookDescription": "hotmails offer", "hookPrice": 313, "isLoading": false, "isOfferDetailsExpanded": false, "isPostingOwner": false, "offerDescription": "hotmails offer", "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "offerPrice": "313", "offerStatus": "withdrawn", "restrictionEndDate": null, "userScore": 100, "usingHookData": true}
LOG  [OfferDetailsSection][REFRESH_BUTTON_CHECK] {"currentUserId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "isCurrentUserOfferOwner": true, "offerOwnerId": "ybEyGDxbvleCEXdTvrA2CgVTJ1y2", "shouldShow": false, "showRefresh": false, "timestamp": "2025-06-04T10:40:44.684Z"}
LOG  [OfferDetailsSection][OFFER_UPDATED] {"description": "hotmails offer", "offerId": "6JYdQTAGUkApzGNWb84b", "price": 313, "timestamp": "2025-06-04T10:40:44.687Z"}
LOG  [OfferCache][SET] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.732Z"}
LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "6JYdQTAGUkApzGNWb84b", "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:40:44.732Z"}
LOG  [OfferCache][SET] {"offerId": "6JYdQTAGUkApzGNWb84b", "timestamp": "2025-06-04T10:40:44.735Z"}
LOG  [useOfferDetails][FETCH_OFFER_SUCCESS] {"offerId": "6JYdQTAGUkApzGNWb84b", "postingId": "v8sq7oTdtyuHwQo42gxY", "timestamp": "2025-06-04T10:40:44.735Z"}
LOG  [fetchPostingsBySearch] Query results: 4
LOG  [useControlledPostings] Fetched postings: 4
LOG  [Navigation] Rendering headerLeft for: Notifications
LOG  [Navigation] Can go back status: true
LOG  [Navigation] Rendering headerLeft for: OfferDetail
LOG  [Navigation] Can go back status: true