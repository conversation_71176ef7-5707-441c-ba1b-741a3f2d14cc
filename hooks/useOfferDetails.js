// useOfferDetails.js

import { useState, useEffect, useRef, useCallback } from 'react';
import { doc, getDoc, getDocs, collection, query, where, orderBy, limit, onSnapshot } from 'firebase/firestore';
import { db } from '../firebase';
import { auth } from '../firebase';

// Simple in-memory cache for offer details
const offerCache = {
  data: new Map(),

  // Set data in cache with timestamp
  set: function(offerId, data) {
    console.log('[OfferCache][SET]', {
      offerId,
      timestamp: new Date().toISOString()
    });
    this.data.set(offerId, {
      data,
      timestamp: Date.now()
    });
  },

  // Get data from cache if not expired (5 minutes)
  get: function(offerId) {
    const cached = this.data.get(offerId);
    if (!cached) return null;

    // Check if cache is expired (5 minutes)
    const now = Date.now();
    const isExpired = now - cached.timestamp > 5 * 60 * 1000;

    console.log('[OfferCache][GET]', {
      offerId,
      found: !!cached,
      isExpired,
      age: now - cached.timestamp,
      timestamp: new Date().toISOString()
    });

    return isExpired ? null : cached.data;
  },

  // Clear cache for specific offer
  clear: function(offerId) {
    console.log('[OfferCache][CLEAR]', {
      offerId,
      timestamp: new Date().toISOString()
    });
    this.data.delete(offerId);
  },

  // Clear all cache
  clearAll: function() {
    console.log('[OfferCache][CLEAR_ALL]', {
      size: this.data.size,
      timestamp: new Date().toISOString()
    });
    this.data.clear();
  }
};

// Listener registry implementation
const listenerRegistry = {
  activeListeners: new Map(),
  addListener: function(id, cleanupFn) {
    console.log('[ListenerRegistry][ADD]', {
      id,
      timestamp: new Date().toISOString(),
      activeCount: this.activeListeners.size + 1
    });
    this.activeListeners.set(id, {
      cleanup: cleanupFn,
      createdAt: new Date().toISOString(),
      lastActive: new Date().toISOString()
    });
  },
  removeListener: function(id) {
    if (this.activeListeners.has(id)) {
      console.log('[ListenerRegistry][REMOVE]', {
        id,
        timestamp: new Date().toISOString(),
        activeCount: this.activeListeners.size - 1
      });
      this.activeListeners.delete(id);
    }
  },
  cleanupAll: function() {
    console.log('[ListenerRegistry][CLEANUP_ALL]', {
      timestamp: new Date().toISOString(),
      activeCount: this.activeListeners.size
    });
    this.activeListeners.forEach((listener, id) => {
      try {
        listener.cleanup();
        console.log('[ListenerRegistry][CLEANUP_SUCCESS]', {
          id,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('[ListenerRegistry][CLEANUP_ERROR]', {
          id,
          error: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString()
        });
      }
    });
    this.activeListeners.clear();
  }
};

const useOfferDetails = (offerId, options = {}) => {
  // Replace the single state object with individual state variables
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [offerDetails, setOfferDetails] = useState(null);
  const [messages, setMessages] = useState([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefreshTime, setLastRefreshTime] = useState(null);

  const { initialOffer, initialPosting, isToggling = false } = options;
  const fetchCountRef = useRef(0);
  const isMountedRef = useRef(true);

  // Add ref to track active listeners
  const listenersRef = useRef({
    offer: null,
    isCleaningUp: false
  });

  // Consolidated cleanup function with additional safeguards
  const cleanupFn = useCallback(() => {
    // Skip if already cleaning up
    if (listenersRef.current.isCleaningUp) {
      console.log('[useOfferDetails][CLEANUP] Already cleaning up, skipping');
      return;
    }

    // Set cleanup flag
    listenersRef.current.isCleaningUp = true;

    console.log('[useOfferDetails][CLEANUP]', {
      offerId,
      hasOfferListener: !!listenersRef.current.offer,
      timestamp: new Date().toISOString()
    });

    try {
      // Cleanup offer listener
      if (listenersRef.current.offer) {
        listenersRef.current.offer();
        listenersRef.current.offer = null;
      }
    } catch (error) {
      console.error('[useOfferDetails][CLEANUP_ERROR]', {
        offerId,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }

    // Mark component as unmounted
    isMountedRef.current = false;

    // Reset cleanup flag after a brief delay to prevent rapid re-initialization
    setTimeout(() => {
      listenersRef.current.isCleaningUp = false;
    }, 150);

    console.log('[useOfferDetails][CLEANUP_COMPLETE]', {
      offerId,
      timestamp: new Date().toISOString()
    });
  }, []); // Empty dependency array to ensure stable reference

  // Store offerId in a ref to maintain stable function references
  const offerIdRef = useRef(offerId);

  // Update ref when offerId changes
  useEffect(() => {
    offerIdRef.current = offerId;
  }, [offerId]);

  // Track if we're toggling the expanded state
  const isTogglingRef = useRef(false);

  // Update the toggling ref when the option changes
  useEffect(() => {
    isTogglingRef.current = isToggling;

    console.log('[useOfferDetails][TOGGLING_STATE]', {
      offerId,
      isToggling,
      timestamp: new Date().toISOString()
    });
  }, [offerId, isToggling]);

  // One-time fetch function for offer details with stable reference and caching
  const fetchOfferDetails = useCallback(async (options = { forceRefresh: false, isToggling: false }) => {
    // Use the ref value instead of the prop directly
    const currentOfferId = offerIdRef.current;
    const { forceRefresh = false, isToggling = false } = options;

    // Update toggling state
    isTogglingRef.current = isToggling;

    if (!currentOfferId || !isMountedRef.current) return null;

    try {
      // Check cache first if not forcing refresh
      if (!forceRefresh) {
        const cachedData = offerCache.get(currentOfferId);
        if (cachedData) {
          console.log('[useOfferDetails][USING_CACHED_DATA]', {
            offerId: currentOfferId,
            timestamp: new Date().toISOString()
          });

          if (isMountedRef.current) {
            setOfferDetails(cachedData);
            setLoading(false);
            // Don't update lastRefreshTime for cached data
          }

          return cachedData;
        }
      }

      // Skip fetching if we're just toggling the expanded state
      if (isToggling) {
        console.log('[useOfferDetails][SKIPPING_FETCH_TOGGLE]', {
          offerId: currentOfferId,
          timestamp: new Date().toISOString()
        });
        return offerDetailsRef.current;
      }

      console.log('[useOfferDetails][FETCH_OFFER_START]', {
        offerId: currentOfferId,
        forceRefresh,
        timestamp: new Date().toISOString()
      });

      const offerRef = doc(db, 'offers', currentOfferId);
      const offerDoc = await getDoc(offerRef);

      if (!offerDoc.exists()) {
        console.log('[useOfferDetails][FETCH_OFFER_NOT_FOUND]', {
          offerId: currentOfferId,
          timestamp: new Date().toISOString()
        });

        if (isMountedRef.current) {
          setError('Offer not found');
          setLoading(false);
        }
        return null;
      }

      const offerData = { id: offerDoc.id, ...offerDoc.data() };

      // Fetch associated posting if not provided in initialPosting
      let postingData = null;
      if (offerData.postingId) {
        const postingDoc = await getDoc(doc(db, 'postings', offerData.postingId));
        if (postingDoc.exists()) {
          postingData = { id: postingDoc.id, ...postingDoc.data() };
        }
      }

      const completeOfferDetails = {
        ...offerData,
        posting: postingData
      };

      // Store in cache
      offerCache.set(currentOfferId, completeOfferDetails);

      console.log('[useOfferDetails][FETCH_OFFER_SUCCESS]', {
        offerId: currentOfferId,
        postingId: offerData.postingId,
        timestamp: new Date().toISOString()
      });

      if (isMountedRef.current) {
        setOfferDetails(completeOfferDetails);
        setLoading(false);
        setLastRefreshTime(new Date());
      }

      return completeOfferDetails;
    } catch (error) {
      console.error('[useOfferDetails][FETCH_OFFER_ERROR]', {
        offerId: currentOfferId,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      });

      if (isMountedRef.current) {
        setError(error.message);
        setLoading(false);
      }

      return null;
    }
  }, []); // Empty dependency array for stable reference

  // Store offerDetails in a ref to maintain stable references
  const offerDetailsRef = useRef(offerDetails);

  // Update ref when offerDetails changes
  useEffect(() => {
    offerDetailsRef.current = offerDetails;
  }, [offerDetails]);

  // Manual refresh function for UI refresh button with stable reference
  const refreshData = useCallback(async () => {
    // Use the ref values instead of the props directly
    const currentOfferId = offerIdRef.current;

    if (!currentOfferId || !isMountedRef.current) return;

    console.log('[useOfferDetails][REFRESH_START]', {
      offerId: currentOfferId,
      timestamp: new Date().toISOString()
    });

    // Set refreshing state but don't update UI yet
    setIsRefreshing(true);

    try {
      // Clear cache for this offer to force a fresh fetch
      offerCache.clear(currentOfferId);

      // Fetch new data with forceRefresh option
      const refreshedOffer = await fetchOfferDetails({ forceRefresh: true });

      if (refreshedOffer) {
        // Get the current offer details from ref
        const currentOfferDetails = offerDetailsRef.current;

        // Compare with current data to see if anything changed
        const hasChanges = !currentOfferDetails ||
          refreshedOffer.price !== currentOfferDetails.price ||
          refreshedOffer.description !== currentOfferDetails.description ||
          refreshedOffer.status !== currentOfferDetails.status;

        if (hasChanges) {
          console.log('[useOfferDetails][DATA_CHANGED]', {
            offerId: currentOfferId,
            oldPrice: currentOfferDetails?.price,
            newPrice: refreshedOffer.price,
            oldDescription: currentOfferDetails?.description?.substring(0, 20) + '...',
            newDescription: refreshedOffer.description?.substring(0, 20) + '...',
            timestamp: new Date().toISOString()
          });

          // Only update the state if there are actual changes and component is still mounted
          if (isMountedRef.current) {
            setOfferDetails(refreshedOffer);
          }
        } else {
          console.log('[useOfferDetails][NO_CHANGES]', {
            offerId: currentOfferId,
            timestamp: new Date().toISOString()
          });
        }

        setLastRefreshTime(new Date());
        return refreshedOffer;
      }
    } catch (error) {
      console.error('[useOfferDetails][REFRESH_ERROR]', {
        offerId: currentOfferId,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      });

      // Don't set the error state on refresh to avoid affecting the UI
    } finally {
      if (isMountedRef.current) {
        setIsRefreshing(false);
      }
    }
  }, []); // Empty dependency array for stable reference

  // Store initialOffer and initialPosting in refs to maintain stable references
  const initialOfferRef = useRef(initialOffer);
  const initialPostingRef = useRef(initialPosting);

  // Update refs when props change
  useEffect(() => {
    initialOfferRef.current = initialOffer;
    initialPostingRef.current = initialPosting;
  }, [initialOffer, initialPosting]);

  // Main effect for data fetching and listener setup - with stable references and caching
  useEffect(() => {
    // Set mounted flag
    isMountedRef.current = true;

    // Add a flag to track if this is the first render
    const isFirstRender = fetchCountRef.current === 0;
    const currentOfferId = offerIdRef.current;
    const currentInitialOffer = initialOfferRef.current;
    const currentInitialPosting = initialPostingRef.current;

    // Check if we're toggling the expanded state
    const isToggling = isTogglingRef.current;

    console.log('[useOfferDetails][EFFECT_TRIGGERED]', {
      offerId: currentOfferId,
      fetchCount: fetchCountRef.current,
      isFirstRender,
      isToggling,
      hasInitialOffer: !!currentInitialOffer,
      timestamp: new Date().toISOString()
    });

    // Skip fetching if we're just toggling the expanded state
    if (isToggling) {
      console.log('[useOfferDetails][SKIPPING_FETCH_FOR_TOGGLE]', {
        offerId: currentOfferId,
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Prevent rapid re-initialization if already cleaning up
    if (listenersRef.current.isCleaningUp) {
      console.log('[useOfferDetails][SKIP_DURING_CLEANUP]', {
        offerId: currentOfferId,
        timestamp: new Date().toISOString()
      });
      return;
    }

    const fetchData = async () => {
      try {
        // If we have initial data, use it instead of fetching
        if (currentInitialOffer) {
          console.log('[useOfferDetails][INITIAL_STATE]', {
            offerId: currentOfferId,
            offerOwnerId: currentInitialOffer.userId,
            postingOwnerId: currentInitialPosting?.userId || currentInitialOffer.posting?.userId,
            timestamp: new Date().toISOString()
          });

          if (isMountedRef.current) {
            setLoading(false);
            setError(null);
            setOfferDetails(currentInitialOffer);
            setMessages([]);
            setLastRefreshTime(new Date());
          }

          // Store initial data in cache
          offerCache.set(currentOfferId, currentInitialOffer);
          return;
        }

        // Check cache first, but invalidate if offer might be withdrawn
        const cachedData = offerCache.get(currentOfferId);
        if (cachedData) {
          // Check if cached data might be stale for withdrawn offers
          const shouldInvalidateCache = cachedData.status === 'pending' &&
            (currentInitialOffer?.status === 'withdrawn' ||
             // Force fresh data if navigating from notification (common case for withdrawn offers)
             window?.location?.href?.includes('notification') ||
             // Also check if we have system messages indicating withdrawal
             cachedData.posting?.postingStatus === 'Deleted');

          if (shouldInvalidateCache) {
            console.log('[useOfferDetails][CACHE_INVALIDATED]', {
              offerId: currentOfferId,
              reason: 'Potential withdrawn offer with stale cache',
              cachedStatus: cachedData.status,
              postingStatus: cachedData.posting?.postingStatus,
              timestamp: new Date().toISOString()
            });
            offerCache.clear(currentOfferId);
          } else {
            console.log('[useOfferDetails][USING_CACHED_DATA_EFFECT]', {
              offerId: currentOfferId,
              cachedStatus: cachedData.status,
              timestamp: new Date().toISOString()
            });

            if (isMountedRef.current) {
              setOfferDetails(cachedData);
              setLoading(false);
              // Don't update lastRefreshTime for cached data
            }
            return;
          }
        }

        // Only fetch on first render or if explicitly needed
        if (isFirstRender || fetchCountRef.current === 0) {
          fetchCountRef.current += 1;

          console.log('[useOfferDetails][FETCHING_DATA]', {
            offerId: currentOfferId,
            fetchCount: fetchCountRef.current,
            timestamp: new Date().toISOString()
          });

          // Use one-time fetch for initial data
          await fetchOfferDetails();

          // Set up real-time listener for offer status changes (especially for withdrawn offers)
          if (!listenersRef.current.offer && isMountedRef.current) {
            console.log('[useOfferDetails][SETTING_UP_LISTENER]', {
              offerId: currentOfferId,
              timestamp: new Date().toISOString()
            });

            const offerRef = doc(db, 'offers', currentOfferId);
            const unsubscribe = onSnapshot(offerRef, (doc) => {
              if (!isMountedRef.current) return;

              if (doc.exists()) {
                const updatedOfferData = { id: doc.id, ...doc.data() };

                console.log('[useOfferDetails][REAL_TIME_UPDATE]', {
                  offerId: currentOfferId,
                  status: updatedOfferData.status,
                  timestamp: new Date().toISOString()
                });

                // Update cache and state with fresh data
                offerCache.set(currentOfferId, updatedOfferData);
                setOfferDetails(prev => ({
                  ...prev,
                  ...updatedOfferData
                }));
              }
            }, (error) => {
              console.error('[useOfferDetails][LISTENER_ERROR]', {
                offerId: currentOfferId,
                error: error.message,
                timestamp: new Date().toISOString()
              });
            });

            listenersRef.current.offer = unsubscribe;
          }
        } else {
          console.log('[useOfferDetails][SKIPPING_FETCH]', {
            offerId: currentOfferId,
            fetchCount: fetchCountRef.current,
            timestamp: new Date().toISOString()
          });
        }

      } catch (error) {
        if (!isMountedRef.current) return;

        console.error('[useOfferDetails][ERROR_STATE]', {
          offerId: currentOfferId,
          error: error.message,
          timestamp: new Date().toISOString()
        });

        setLoading(false);
        setError(error.message);
        setOfferDetails(null);
        setMessages([]);
      }
    };

    fetchData();

    // Return the stable cleanup function
    return cleanupFn;
  }, [offerId]); // Only re-run when offerId changes

  return {
    loading,
    error,
    offerDetails,
    messages,
    setOfferDetails,
    refreshData,
    isRefreshing,
    lastRefreshTime,
    cleanup: cleanupFn
  };
};

export default useOfferDetails;
